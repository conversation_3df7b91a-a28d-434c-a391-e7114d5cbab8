import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON>rowserRouter } from "react-router";
import "./index.css";
import "./App.css";
import Routers from "./routes/routers.jsx";
import AuthProvider from "./providers/authprovider.jsx";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient();

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <AuthProvider>
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <Routers />
        </BrowserRouter>
      </QueryClientProvider>
    </AuthProvider>
  </StrictMode>
);
