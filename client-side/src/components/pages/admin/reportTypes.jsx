import React, { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { FaPlus, FaEdit, FaTrash, FaToggleOn, <PERSON>a<PERSON><PERSON><PERSON><PERSON>ff, FaSort } from "react-icons/fa";
import useAxiosSecure from "../../../hooks/useaxiossecure";
import { showToast } from "../../utils/toasters/toastService";
import ReportTypeModal from "../../utils/modals/reportTypeModal";

const ReportTypes = () => {
  const axiosSecure = useAxiosSecure();
  const queryClient = useQueryClient();
  const [showModal, setShowModal] = useState(false);
  const [editingReportType, setEditingReportType] = useState(null);
  const [includeInactive, setIncludeInactive] = useState(false);

  // Fetch report types
  const { data: reportTypesData, isLoading, error } = useQuery({
    queryKey: ["reportTypes", includeInactive],
    queryFn: async () => {
      const response = await axiosSecure.get(`/api/report-types?includeInactive=${includeInactive}`);
      return response.data;
    },
  });

  // Initialize default report types
  const initializeMutation = useMutation({
    mutationFn: async () => {
      const response = await axiosSecure.post("/api/report-types/initialize");
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries(["reportTypes"]);
      showToast("success", "Default report types initialized successfully");
    },
    onError: (error) => {
      showToast("error", error.response?.data?.error || "Failed to initialize report types");
    },
  });

  // Toggle report type active status
  const toggleActiveMutation = useMutation({
    mutationFn: async ({ key, isActive }) => {
      const response = await axiosSecure.put(`/api/report-types/${key}`, { isActive });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["reportTypes"]);
      showToast("success", "Report type updated successfully");
    },
    onError: (error) => {
      showToast("error", error.response?.data?.error || "Failed to update report type");
    },
  });

  // Delete report type
  const deleteMutation = useMutation({
    mutationFn: async (key) => {
      const response = await axiosSecure.delete(`/api/report-types/${key}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["reportTypes"]);
      showToast("success", "Report type deactivated successfully");
    },
    onError: (error) => {
      showToast("error", error.response?.data?.error || "Failed to deactivate report type");
    },
  });

  const handleEdit = (reportType) => {
    setEditingReportType(reportType);
    setShowModal(true);
  };

  const handleAdd = () => {
    setEditingReportType(null);
    setShowModal(true);
  };

  const handleToggleActive = (reportType) => {
    toggleActiveMutation.mutate({
      key: reportType.key,
      isActive: !reportType.isActive,
    });
  };

  const handleDelete = (reportType) => {
    if (window.confirm(`Are you sure you want to deactivate "${reportType.name}"?`)) {
      deleteMutation.mutate(reportType.key);
    }
  };

  const reportTypes = reportTypesData?.data || [];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error loading report types: {error.message}</p>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Report Types</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage available report types for submissions
          </p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={() => initializeMutation.mutate()}
            disabled={initializeMutation.isPending}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
          >
            {initializeMutation.isPending ? "Initializing..." : "Initialize Defaults"}
          </button>
          <button
            onClick={handleAdd}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <FaPlus /> Add Report Type
          </button>
        </div>
      </div>

      <div className="mb-4">
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={includeInactive}
            onChange={(e) => setIncludeInactive(e.target.checked)}
            className="rounded"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Show inactive report types
          </span>
        </label>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                <div className="flex items-center gap-1">
                  <FaSort className="text-xs" />
                  Order
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Key
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Description
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {reportTypes.map((reportType) => (
              <tr key={reportType.key} className={!reportType.isActive ? "opacity-60" : ""}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {reportType.sortOrder}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-sm">
                    {reportType.key}
                  </code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                  {reportType.name}
                </td>
                <td className="px-6 py-4 text-sm text-gray-600 dark:text-gray-400">
                  {reportType.description}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={() => handleToggleActive(reportType)}
                    className={`flex items-center gap-1 px-2 py-1 rounded text-sm ${
                      reportType.isActive
                        ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                        : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                    }`}
                  >
                    {reportType.isActive ? <FaToggleOn /> : <FaToggleOff />}
                    {reportType.isActive ? "Active" : "Inactive"}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => handleEdit(reportType)}
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      <FaEdit />
                    </button>
                    <button
                      onClick={() => handleDelete(reportType)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                    >
                      <FaTrash />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {reportTypes.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-500 dark:text-gray-400">No report types found.</p>
            <button
              onClick={() => initializeMutation.mutate()}
              className="mt-2 text-blue-600 hover:text-blue-800 dark:text-blue-400"
            >
              Initialize default report types
            </button>
          </div>
        )}
      </div>

      {showModal && (
        <ReportTypeModal
          reportType={editingReportType}
          onClose={() => {
            setShowModal(false);
            setEditingReportType(null);
          }}
          onSuccess={() => {
            queryClient.invalidateQueries(["reportTypes"]);
            setShowModal(false);
            setEditingReportType(null);
          }}
        />
      )}
    </div>
  );
};

export default ReportTypes;
