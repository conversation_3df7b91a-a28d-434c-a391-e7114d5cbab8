import React, { useState } from "react";
import ModernIntegrationCard from "./ModernIntegrationCard";
import IntegrationSettingsModal from "./IntegrationSettingsModal";
import { FaSlack, FaVideo } from "react-icons/fa";

/**
 * Test component to verify integration components are working
 */
const IntegrationTest = () => {
  const [showModal, setShowModal] = useState(false);
  const [slackEnabled, setSlackEnabled] = useState(true);
  const [zoomEnabled, setZoomEnabled] = useState(false);

  const testIntegration = {
    id: "slack",
    name: "Slack",
    icon: FaSlack,
    description: "Test integration for Slack",
    features: [
      "Channel notifications",
      "Meeting summaries",
      "Action item tracking",
      "Team collaboration"
    ],
    isEnabled: slackEnabled,
    status: slackEnabled ? "connected" : "disconnected",
    lastSync: slackEnabled ? new Date().toISOString() : null
  };

  const handleSlackToggle = async (enabled) => {
    console.log("Toggling Slack:", enabled);
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    setSlackEnabled(enabled);
  };

  const handleZoomToggle = async (enabled) => {
    console.log("Toggling Zoom:", enabled);
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    setZoomEnabled(enabled);
  };

  const handleShowSettings = () => {
    setShowModal(true);
  };

  return (
    <div className="p-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-8">
          Integration Components Test
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Slack Integration Card */}
          <ModernIntegrationCard
            icon={FaSlack}
            name="Slack"
            description="Get instant meeting summaries & action items in your team channels."
            isEnabled={slackEnabled}
            onToggle={handleSlackToggle}
            onSettings={handleShowSettings}
          />

          {/* Zoom Integration Card */}
          <ModernIntegrationCard
            icon={FaVideo}
            name="Zoom"
            description="AI-powered meeting transcriptions & summaries."
            isEnabled={zoomEnabled}
            onToggle={handleZoomToggle}
            onSettings={handleShowSettings}
          />
        </div>

        {/* Test Results */}
        <div className="mt-8 p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Test Status
          </h2>
          <div className="space-y-2 text-sm">
            <p className="text-gray-600 dark:text-gray-400">
              Slack Status: <span className={slackEnabled ? "text-green-600" : "text-red-600"}>
                {slackEnabled ? "Enabled" : "Disabled"}
              </span>
            </p>
            <p className="text-gray-600 dark:text-gray-400">
              Zoom Status: <span className={zoomEnabled ? "text-green-600" : "text-red-600"}>
                {zoomEnabled ? "Enabled" : "Disabled"}
              </span>
            </p>
          </div>
        </div>

        {/* Settings Modal */}
        <IntegrationSettingsModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          integration={testIntegration}
        />
      </div>
    </div>
  );
};

export default IntegrationTest;
