import React from "react";
import { FaEdit, FaPlay } from "react-icons/fa";

/**
 * Badge component for displaying connection status
 */
const StatusBadge = ({ status, className = "" }) => {
  const getStatusConfig = () => {
    switch (status) {
      case "connected":
        return {
          bgColor: "bg-green-100 dark:bg-green-900/20",
          textColor: "text-green-800 dark:text-green-200",
          borderColor: "border-green-200 dark:border-green-800",
          label: "Connected"
        };
      case "disconnected":
        return {
          bgColor: "bg-gray-100 dark:bg-gray-900/20",
          textColor: "text-gray-800 dark:text-gray-200",
          borderColor: "border-gray-200 dark:border-gray-800",
          label: "Disconnected"
        };
      case "error":
        return {
          bgColor: "bg-red-100 dark:bg-red-900/20",
          textColor: "text-red-800 dark:text-red-200",
          borderColor: "border-red-200 dark:border-red-800",
          label: "Error"
        };
      case "pending":
        return {
          bgColor: "bg-amber-100 dark:bg-amber-900/20",
          textColor: "text-amber-800 dark:text-amber-200",
          borderColor: "border-amber-200 dark:border-amber-800",
          label: "Pending"
        };
      default:
        return {
          bgColor: "bg-gray-100 dark:bg-gray-900/20",
          textColor: "text-gray-800 dark:text-gray-200",
          borderColor: "border-gray-200 dark:border-gray-800",
          label: "Unknown"
        };
    }
  };

  const config = getStatusConfig();

  return (
    <span
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.bgColor} ${config.textColor} ${config.borderColor} ${className}`}
    >
      {config.label}
    </span>
  );
};

/**
 * Icon button component for actions
 */
const IconButton = ({ icon: Icon, onClick, ariaLabel, disabled = false, className = "" }) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      aria-label={ariaLabel}
      className={`p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
    >
      <Icon className="w-4 h-4" />
    </button>
  );
};

/**
 * Base IntegrationCard component
 * Reusable card layout for any integration type
 */
const IntegrationCard = ({
  // Header props
  integrationName,
  icon: IntegrationIcon,
  status = "disconnected",
  subtitle,
  onEdit,
  onTest,
  
  // Body content
  children,
  
  // Footer props
  footer,
  
  // Styling
  className = "",
  maxWidth = "max-w-lg"
}) => {
  return (
    <div className={`${maxWidth} w-full bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            {IntegrationIcon && (
              <div className="flex-shrink-0">
                <IntegrationIcon className="w-6 h-6 text-gray-700 dark:text-gray-300" />
              </div>
            )}
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {integrationName}
            </h2>
          </div>
          
          <div className="flex items-center gap-2">
            <StatusBadge status={status} />
            {subtitle && (
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {subtitle}
              </span>
            )}
          </div>
        </div>
        
        {/* Action buttons */}
        <div className="flex gap-1 ml-4">
          {onTest && (
            <IconButton
              icon={FaPlay}
              onClick={onTest}
              ariaLabel="Test connection"
              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20"
            />
          )}
          {onEdit && (
            <IconButton
              icon={FaEdit}
              onClick={onEdit}
              ariaLabel="Edit integration"
            />
          )}
        </div>
      </div>

      {/* Body content */}
      {children && (
        <div className="space-y-4">
          {children}
        </div>
      )}

      {/* Footer */}
      {footer && (
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          {footer}
        </div>
      )}
    </div>
  );
};

export default IntegrationCard;
export { StatusBadge, IconButton };
