import React, { useState } from "react";
import { FaPlay, FaCheckCircle, FaExclamationTriangle, FaSpinner } from "react-icons/fa";
import { useSlackConnectionTest } from "../../../hooks/useSlackIntegration";

/**
 * Inline connection test component with immediate feedback
 * No full-screen takeover, uses toast notifications and inline status
 */
const ConnectionTestPanel = ({ 
  botToken, 
  onTestSuccess, 
  onTestError,
  className = "" 
}) => {
  const [testResult, setTestResult] = useState(null);
  const connectionTestMutation = useSlackConnectionTest();

  const handleTest = async () => {
    if (!botToken || !botToken.trim()) {
      setTestResult({
        success: false,
        message: "Please enter a bot token first"
      });
      return;
    }

    setTestResult(null);
    
    try {
      const result = await connectionTestMutation.mutateAsync({ botToken });
      setTestResult({
        success: true,
        message: "Connection successful!",
        data: result.data
      });
      onTestSuccess?.(result);
    } catch (error) {
      const message = error.response?.data?.error || "Connection test failed";
      setTestResult({
        success: false,
        message
      });
      onTestError?.(error);
    }
  };

  const getStatusIcon = () => {
    if (connectionTestMutation.isPending) {
      return <FaSpinner className="animate-spin text-blue-500" />;
    }
    if (testResult?.success) {
      return <FaCheckCircle className="text-green-500" />;
    }
    if (testResult && !testResult.success) {
      return <FaExclamationTriangle className="text-red-500" />;
    }
    return <FaPlay className="text-gray-500" />;
  };

  const getStatusColor = () => {
    if (connectionTestMutation.isPending) {
      return "border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20";
    }
    if (testResult?.success) {
      return "border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20";
    }
    if (testResult && !testResult.success) {
      return "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20";
    }
    return "border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800";
  };

  const getTextColor = () => {
    if (connectionTestMutation.isPending) {
      return "text-blue-700 dark:text-blue-300";
    }
    if (testResult?.success) {
      return "text-green-700 dark:text-green-300";
    }
    if (testResult && !testResult.success) {
      return "text-red-700 dark:text-red-300";
    }
    return "text-gray-700 dark:text-gray-300";
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Test Button */}
      <div className="flex items-center gap-3">
        <button
          onClick={handleTest}
          disabled={connectionTestMutation.isPending || !botToken?.trim()}
          className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
        >
          {connectionTestMutation.isPending ? (
            <FaSpinner className="animate-spin w-4 h-4" />
          ) : (
            <FaPlay className="w-4 h-4" />
          )}
          {connectionTestMutation.isPending ? "Testing..." : "Test Connection"}
        </button>
        
        <div className="text-xs text-gray-500 dark:text-gray-400">
          Verify your bot token works
        </div>
      </div>

      {/* Inline Status Display */}
      {(connectionTestMutation.isPending || testResult) && (
        <div className={`p-3 rounded-lg border ${getStatusColor()}`}>
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              {getStatusIcon()}
            </div>
            <div className="flex-1">
              <div className={`text-sm font-medium ${getTextColor()}`}>
                {connectionTestMutation.isPending && "Testing connection..."}
                {testResult?.success && "Connection Successful"}
                {testResult && !testResult.success && "Connection Failed"}
              </div>
              
              {testResult && (
                <div className={`text-sm mt-1 ${getTextColor()}`}>
                  {testResult.message}
                </div>
              )}
              
              {testResult?.success && testResult.data && (
                <div className="mt-2 space-y-1">
                  {testResult.data.teamName && (
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      <span className="font-medium">Workspace:</span> {testResult.data.teamName}
                    </div>
                  )}
                  {testResult.data.botUserId && (
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      <span className="font-medium">Bot ID:</span> {testResult.data.botUserId}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="text-xs text-gray-500 dark:text-gray-400">
        <p>
          💡 <strong>Tip:</strong> Make sure your bot token starts with "xoxb-" and has the necessary permissions.
        </p>
      </div>
    </div>
  );
};

/**
 * Quick test button for existing connections
 */
export const QuickTestButton = ({ 
  onTest, 
  isLoading = false, 
  disabled = false,
  size = "sm",
  variant = "outline"
}) => {
  const sizeClasses = {
    xs: "px-2 py-1 text-xs",
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-sm"
  };

  const variantClasses = {
    outline: "border border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800",
    solid: "bg-blue-600 text-white hover:bg-blue-700",
    ghost: "text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800"
  };

  return (
    <button
      onClick={onTest}
      disabled={isLoading || disabled}
      className={`
        flex items-center gap-1 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed
        ${sizeClasses[size]}
        ${variantClasses[variant]}
      `}
    >
      {isLoading ? (
        <FaSpinner className="animate-spin w-3 h-3" />
      ) : (
        <FaPlay className="w-3 h-3" />
      )}
      {isLoading ? "Testing..." : "Test"}
    </button>
  );
};

export default ConnectionTestPanel;
