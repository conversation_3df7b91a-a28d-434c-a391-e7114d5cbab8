import React, { useState } from "react";
import BreadCrumb from "../common/breadcrumb";
import SlackIntegration from "./slack/slackIntegration";
import ReportTypes from "../admin/reportTypes";
import { FaSlack, FaList } from "react-icons/fa";
import { MdIntegrationInstructions } from "react-icons/md";

const Integrations = () => {
  const [activeTab, setActiveTab] = useState("slack");

  const integrationTabs = [
    {
      id: "slack",
      name: "Slack",
      icon: <FaSlack className="text-lg" />,
      component: <SlackIntegration />,
      description:
        "Connect your workspace to Slack for automated report posting",
    },
    {
      id: "report-types",
      name: "Report Types",
      icon: <FaList className="text-lg" />,
      component: <ReportTypes />,
      description: "Manage available report types for submissions",
    },
    // Future integrations can be added here
    // {
    //   id: "teams",
    //   name: "Microsoft Teams",
    //   icon: <SiMicrosoftteams className="text-lg" />,
    //   component: <TeamsIntegration />,
    //   description: "Connect to Microsoft Teams"
    // }
  ];

  return (
    <>
      <BreadCrumb bTitle="Integrations" bText="Manage External Integrations" />

      <div className="space-y-6">
        {/* Integration Overview */}
        <div className="overflow-hidden rounded-2xl border border-gray-200 bg-white p-6 dark:border-white/[0.05] dark:bg-white/[0.03]">
          <div className="flex items-center gap-3 mb-4">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-50 dark:bg-blue-900/20">
              <MdIntegrationInstructions className="text-xl text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                External Integrations
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Connect your workspace with external services to automate
                workflows
              </p>
            </div>
          </div>

          {/* Integration Tabs */}
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              {integrationTabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? "border-blue-500 text-blue-600 dark:text-blue-400"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                  }`}
                >
                  {tab.icon}
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Active Integration Content */}
        <div className="space-y-6">
          {integrationTabs.map((tab) => (
            <div
              key={tab.id}
              className={activeTab === tab.id ? "block" : "hidden"}
            >
              {tab.component}
            </div>
          ))}
        </div>

        {/* Help Section */}
        <div className="overflow-hidden rounded-2xl border border-gray-200 bg-white p-6 dark:border-white/[0.05] dark:bg-white/[0.03]">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Need Help?
          </h3>
          <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
            <p>
              <strong>Slack Integration:</strong> Automatically post daily work
              reports to designated Slack channels organized by monthly threads.
            </p>
            <p>
              <strong>Setup Requirements:</strong> You'll need admin access to
              your Slack workspace and the ability to create a Slack app with
              bot permissions.
            </p>
            <p>
              <strong>Security:</strong> All integration tokens are encrypted
              and stored securely. Only super admins can manage integrations.
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Integrations;
