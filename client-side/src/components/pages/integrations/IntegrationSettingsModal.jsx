import React from "react";
import { FaTimes, FaCog, FaCheckCircle, FaExclamationTriangle } from "react-icons/fa";

/**
 * Settings modal for integration details and configuration
 */
const IntegrationSettingsModal = ({
  isOpen,
  onClose,
  integration,
  children
}) => {
  if (!isOpen || !integration) return null;

  const getStatusIcon = () => {
    switch (integration.status) {
      case "connected":
        return <FaCheckCircle className="w-5 h-5 text-green-500" />;
      case "error":
        return <FaExclamationTriangle className="w-5 h-5 text-red-500" />;
      default:
        return <FaCog className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = () => {
    switch (integration.status) {
      case "connected":
        return "Connected";
      case "error":
        return "Connection Error";
      case "disconnected":
        return "Disconnected";
      default:
        return "Not Configured";
    }
  };

  const getStatusColor = () => {
    switch (integration.status) {
      case "connected":
        return "text-green-600 dark:text-green-400";
      case "error":
        return "text-red-600 dark:text-red-400";
      default:
        return "text-gray-600 dark:text-gray-400";
    }
  };

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="fixed inset-0 z-50 overflow-hidden">
        <div className="flex min-h-full items-center justify-center p-4">
          <div className="relative w-full max-w-lg bg-white dark:bg-gray-800 rounded-2xl shadow-xl">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-3">
                {integration.icon && (
                  <integration.icon className="w-8 h-8 text-blue-600" />
                )}
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {integration.name}
                  </h2>
                  <div className="flex items-center gap-2 mt-1">
                    {getStatusIcon()}
                    <span className={`text-sm font-medium ${getStatusColor()}`}>
                      {getStatusText()}
                    </span>
                  </div>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <FaTimes className="w-5 h-5" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              {/* Description */}
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  About this integration
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                  {integration.description}
                </p>
              </div>

              {/* Status Details */}
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  Status Details
                </h3>
                <div className="space-y-2">
                  <div className="flex justify-between items-center py-2 px-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Status</span>
                    <div className="flex items-center gap-2">
                      {getStatusIcon()}
                      <span className={`text-sm font-medium ${getStatusColor()}`}>
                        {getStatusText()}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center py-2 px-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Enabled</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {integration.isEnabled ? "Yes" : "No"}
                    </span>
                  </div>

                  {integration.lastSync && (
                    <div className="flex justify-between items-center py-2 px-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Last Sync</span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {new Date(integration.lastSync).toLocaleString()}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Custom Content */}
              {children && (
                <div className="mb-6">
                  {children}
                </div>
              )}

              {/* Features */}
              {integration.features && integration.features.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                    Features
                  </h3>
                  <ul className="space-y-2">
                    {integration.features.map((feature, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <FaCheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="flex justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
              >
                Close
              </button>
              {integration.onConfigure && (
                <button
                  onClick={() => {
                    integration.onConfigure();
                    onClose();
                  }}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                >
                  Configure
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default IntegrationSettingsModal;
