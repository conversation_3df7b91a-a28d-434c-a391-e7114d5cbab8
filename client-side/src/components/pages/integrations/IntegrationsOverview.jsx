import React, { useState } from "react";
import {
  FaSlack,
  FaShopify,
  FaSearch,
  FaPlus,
  FaVideo,
  FaMicrosoft,
  FaGoogle,
  FaDropbox,
  FaCalendarAlt,
  FaEnvelope,
} from "react-icons/fa";
import {
  SiMicrosoftteams,
  SiGooglemeet,
  SiGoogledrive,
  SiOnedrive,
} from "react-icons/si";
import { MdIntegrationInstructions } from "react-icons/md";
import BreadCrumb from "../common/breadcrumb";
import ModernIntegrationCard from "./ModernIntegrationCard";
import IntegrationSettingsModal from "./IntegrationSettingsModal";
import SlackConnectionModal from "./SlackConnectionModal";
import {
  useSlackIntegrationStatus,
  useSlackConnect,
  useSlackDisconnect,
} from "../../../hooks/useSlackIntegration";

/**
 * Integration status management hook
 */
const useIntegrationStatus = () => {
  const [integrationStates, setIntegrationStates] = useState({
    zoom: { enabled: false, status: "disconnected" },
    teams: { enabled: true, status: "connected" },
    googlemeet: { enabled: true, status: "connected" },
    googledrive: { enabled: true, status: "connected" },
    dropbox: { enabled: false, status: "disconnected" },
    onedrive: { enabled: false, status: "disconnected" },
    googlecalendar: { enabled: true, status: "connected" },
    outlook: { enabled: false, status: "disconnected" },
    slack: { enabled: true, status: "connected" },
  });

  const toggleIntegration = async (integrationId, enabled) => {
    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    setIntegrationStates((prev) => ({
      ...prev,
      [integrationId]: {
        ...prev[integrationId],
        enabled,
        status: enabled ? "connected" : "disconnected",
      },
    }));
  };

  return { integrationStates, toggleIntegration };
};

/**
 * Main integrations overview page
 * Displays integration cards in a responsive grid layout
 */
const IntegrationsOverview = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [showSlackConfig, setShowSlackConfig] = useState(false);

  // Available integrations (current and future)
  const integrations = [
    {
      id: "slack",
      name: "Slack",
      icon: FaSlack,
      component: SlackIntegrationCard,
      description: "Post reports to Slack channels automatically",
      available: true,
    },
    {
      id: "shopify",
      name: "Shopify",
      icon: FaShopify,
      description: "Sync with Shopify store data and analytics",
      available: false,
    },
    // Add more integrations here as they become available
  ];

  const filteredIntegrations = integrations.filter((integration) =>
    integration.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSlackEdit = () => {
    setShowSlackConfig(true);
  };

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <BreadCrumb
        title="Integrations"
        icon={<MdIntegrationInstructions className="text-xl" />}
        description="Connect your workspace with external services"
      />

      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Integrations
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Connect and manage your external service integrations
            </p>
          </div>

          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaSearch className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search integrations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
            />
          </div>
        </div>
      </div>

      {/* Integration Cards Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredIntegrations.map((integration) => {
          if (integration.available) {
            // Render actual integration component
            if (integration.id === "slack") {
              return (
                <SlackIntegrationCard
                  key={integration.id}
                  onEdit={handleSlackEdit}
                />
              );
            }
            // Add other available integrations here
          } else {
            // Render placeholder for future integrations
            return (
              <PlaceholderIntegrationCard
                key={integration.id}
                name={integration.name}
                icon={integration.icon}
                description={integration.description}
                comingSoon={true}
              />
            );
          }
        })}

        {/* Add New Integration Placeholder */}
        <div className="max-w-lg w-full bg-gray-50 dark:bg-gray-800/50 rounded-2xl border-2 border-dashed border-gray-300 dark:border-gray-600 p-6 flex flex-col items-center justify-center text-center hover:border-gray-400 dark:hover:border-gray-500 transition-colors cursor-pointer">
          <FaPlus className="text-3xl text-gray-400 mb-3" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Request Integration
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Need an integration that's not listed? Let us know!
          </p>
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
            Contact Support
          </button>
        </div>
      </div>

      {/* No Results */}
      {filteredIntegrations.length === 0 && searchTerm && (
        <div className="text-center py-12">
          <FaSearch className="mx-auto text-4xl text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No integrations found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Try adjusting your search terms or browse all available
            integrations.
          </p>
          <button
            onClick={() => setSearchTerm("")}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Clear Search
          </button>
        </div>
      )}

      {/* Integration Stats */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Integration Statistics
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {integrations.filter((i) => i.available).length}
            </div>
            <div className="text-sm text-green-700 dark:text-green-300">
              Active Integrations
            </div>
          </div>
          <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {integrations.filter((i) => !i.available).length}
            </div>
            <div className="text-sm text-blue-700 dark:text-blue-300">
              Coming Soon
            </div>
          </div>
          <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {integrations.length}
            </div>
            <div className="text-sm text-purple-700 dark:text-purple-300">
              Total Available
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IntegrationsOverview;
