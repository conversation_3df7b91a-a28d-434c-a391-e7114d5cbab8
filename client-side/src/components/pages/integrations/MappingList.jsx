import React, { useState } from "react";
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaHashtag,
  FaChevronDown,
  FaChevronRight,
  FaPlay,
} from "react-icons/fa";

/**
 * Compact mapping list component for integration cards
 */
const MappingList = ({
  mappings = [],
  reportTypes = [],
  onAddMapping,
  onEditMapping,
  onDeleteMapping,
  onTestMapping,
  isLoading = false,
  maxVisibleMappings = 3,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const visibleMappings = isExpanded
    ? mappings
    : mappings.slice(0, maxVisibleMappings);
  const hasMoreMappings = mappings.length > maxVisibleMappings;

  const getReportTypeLabel = (reportType) => {
    const type = reportTypes.find((t) => t.value === reportType);
    return type ? type.label : reportType;
  };

  const handleDeleteClick = (mapping) => {
    if (
      window.confirm("Are you sure you want to delete this channel mapping?")
    ) {
      onDeleteMapping(mapping._id);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Report Type → Channel Mappings
          </div>
          <div className="w-20 h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
        <div className="space-y-2">
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="h-12 bg-gray-100 dark:bg-gray-800 rounded animate-pulse"
            ></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Report Type → Channel Mappings
        </div>
        <button
          onClick={onAddMapping}
          className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          <FaPlus className="w-3 h-3" />
          Add
        </button>
      </div>

      {/* Mappings */}
      {mappings.length === 0 ? (
        <div className="text-center py-4 text-gray-500 dark:text-gray-400">
          <FaHashtag className="mx-auto text-2xl mb-2 opacity-50" />
          <p className="text-sm">No channel mappings configured</p>
          <button
            onClick={onAddMapping}
            className="mt-2 text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
          >
            Add your first mapping
          </button>
        </div>
      ) : (
        <div className="space-y-2">
          {/* Mapping Table */}
          <div className="overflow-hidden">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="text-left py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Report Type
                  </th>
                  <th className="text-left py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Channel
                  </th>
                  <th className="text-right py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {visibleMappings.map((mapping) => (
                  <tr
                    key={mapping._id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-800/50"
                  >
                    <td className="py-2">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-200">
                        {getReportTypeLabel(mapping.reportType)}
                      </span>
                    </td>
                    <td className="py-2">
                      <div className="flex items-center gap-1">
                        <FaHashtag className="text-gray-400 w-3 h-3" />
                        <span className="font-mono text-xs text-gray-600 dark:text-gray-400">
                          {mapping.slackChannelId}
                        </span>
                      </div>
                      {mapping.slackChannelName && (
                        <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                          {mapping.slackChannelName}
                        </div>
                      )}
                    </td>
                    <td className="py-2">
                      <div className="flex items-center justify-end gap-1">
                        {onTestMapping && (
                          <button
                            onClick={() => onTestMapping(mapping)}
                            className="p-1 text-green-600 hover:bg-green-100 rounded dark:text-green-400 dark:hover:bg-green-900/20"
                            title="Test mapping"
                          >
                            <FaPlay className="w-3 h-3" />
                          </button>
                        )}
                        <button
                          onClick={() => onEditMapping(mapping)}
                          className="p-1 text-blue-600 hover:bg-blue-100 rounded dark:text-blue-400 dark:hover:bg-blue-900/20"
                          title="Edit mapping"
                        >
                          <FaEdit className="w-3 h-3" />
                        </button>
                        <button
                          onClick={() => handleDeleteClick(mapping)}
                          className="p-1 text-red-600 hover:bg-red-100 rounded dark:text-red-400 dark:hover:bg-red-900/20"
                          title="Delete mapping"
                        >
                          <FaTrash className="w-3 h-3" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Expand/Collapse button */}
          {hasMoreMappings && (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="w-full flex items-center justify-center gap-2 py-2 text-xs text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 border-t border-gray-200 dark:border-gray-700"
            >
              {isExpanded ? (
                <>
                  <FaChevronDown className="w-3 h-3" />
                  Show Less
                </>
              ) : (
                <>
                  <FaChevronRight className="w-3 h-3" />
                  Show {mappings.length - maxVisibleMappings} More
                </>
              )}
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default MappingList;
