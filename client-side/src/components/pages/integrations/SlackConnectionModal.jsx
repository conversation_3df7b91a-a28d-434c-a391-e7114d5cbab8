import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { FaTimes, FaSlack, Fa<PERSON>ye, FaEyeSlash } from "react-icons/fa";
import { MdSave } from "react-icons/md";
import ConnectionTestPanel from "./ConnectionTestPanel";
import { useSlackConnect, useSlackDisconnect } from "../../../hooks/useSlackIntegration";

/**
 * Compact modal for Slack connection setup
 * Non-full-screen modal for connecting/updating Slack integration
 */
const SlackConnectionModal = ({
  isOpen,
  onClose,
  currentStatus = null
}) => {
  const [showToken, setShowToken] = useState(false);
  const [testResult, setTestResult] = useState(null);
  
  const connectMutation = useSlackConnect();
  const disconnectMutation = useSlackDisconnect();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    reset
  } = useForm();

  const botToken = watch("botToken");
  const isConnected = currentStatus?.connected;

  const handleFormSubmit = (data) => {
    connectMutation.mutate(data, {
      onSuccess: () => {
        reset();
        onClose();
      }
    });
  };

  const handleDisconnect = () => {
    if (window.confirm("Are you sure you want to disconnect the Slack integration?")) {
      disconnectMutation.mutate(undefined, {
        onSuccess: () => {
          onClose();
        }
      });
    }
  };

  const handleClose = () => {
    reset();
    setTestResult(null);
    onClose();
  };

  const handleTestSuccess = (result) => {
    setTestResult({ success: true, data: result.data });
  };

  const handleTestError = (error) => {
    setTestResult({ success: false, error });
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity"
        onClick={handleClose}
      />
      
      {/* Modal */}
      <div className="fixed inset-0 z-50 overflow-hidden">
        <div className="flex min-h-full items-center justify-center p-4">
          <div className="relative w-full max-w-md bg-white dark:bg-gray-800 rounded-2xl shadow-xl">
            {/* Header */}
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 px-6 py-4 rounded-t-2xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <FaSlack className="text-white text-xl" />
                  <h2 className="text-lg font-semibold text-white">
                    Slack Integration
                  </h2>
                </div>
                <button
                  type="button"
                  className="text-white hover:text-gray-200 transition-colors"
                  onClick={handleClose}
                >
                  <FaTimes className="w-5 h-5" />
                </button>
              </div>
              <p className="text-purple-100 text-sm mt-1">
                {isConnected ? "Update your Slack connection" : "Connect your Slack workspace"}
              </p>
            </div>

            {/* Content */}
            <div className="p-6 space-y-6">
              {/* Current Status */}
              {isConnected && currentStatus && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg dark:bg-green-900/20 dark:border-green-800">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium text-green-800 dark:text-green-200">
                        Currently Connected
                      </div>
                      {currentStatus.teamName && (
                        <div className="text-sm text-green-700 dark:text-green-300">
                          Workspace: {currentStatus.teamName}
                        </div>
                      )}
                    </div>
                    <button
                      onClick={handleDisconnect}
                      disabled={disconnectMutation.isPending}
                      className="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
                    >
                      {disconnectMutation.isPending ? "Disconnecting..." : "Disconnect"}
                    </button>
                  </div>
                </div>
              )}

              {/* Form */}
              <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
                {/* Bot Token Input */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Bot Token *
                  </label>
                  <div className="relative">
                    <input
                      type={showToken ? "text" : "password"}
                      {...register("botToken", {
                        required: "Bot token is required",
                        pattern: {
                          value: /^xoxb-/,
                          message: "Bot token must start with 'xoxb-'"
                        }
                      })}
                      className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder="xoxb-your-bot-token-here"
                    />
                    <button
                      type="button"
                      onClick={() => setShowToken(!showToken)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                    >
                      {showToken ? <FaEyeSlash /> : <FaEye />}
                    </button>
                  </div>
                  {errors.botToken && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.botToken.message}
                    </p>
                  )}
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Get your bot token from your Slack app settings
                  </p>
                </div>

                {/* Connection Test */}
                <ConnectionTestPanel
                  botToken={botToken}
                  onTestSuccess={handleTestSuccess}
                  onTestError={handleTestError}
                />

                {/* Submit Button */}
                <div className="flex gap-3 pt-4">
                  <button
                    type="button"
                    onClick={handleClose}
                    className="flex-1 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={connectMutation.isPending || !testResult?.success}
                    className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {connectMutation.isPending ? (
                      <span className="loading loading-spinner loading-sm"></span>
                    ) : (
                      <MdSave />
                    )}
                    {connectMutation.isPending ? "Connecting..." : (isConnected ? "Update" : "Connect")}
                  </button>
                </div>
              </form>

              {/* Help Text */}
              <div className="text-xs text-gray-500 dark:text-gray-400 space-y-2">
                <p>
                  <strong>How to get your bot token:</strong>
                </p>
                <ol className="list-decimal list-inside space-y-1 ml-2">
                  <li>Go to <a href="https://api.slack.com/apps" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">api.slack.com/apps</a></li>
                  <li>Select your app or create a new one</li>
                  <li>Go to "OAuth & Permissions"</li>
                  <li>Copy the "Bot User OAuth Token"</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SlackConnectionModal;
