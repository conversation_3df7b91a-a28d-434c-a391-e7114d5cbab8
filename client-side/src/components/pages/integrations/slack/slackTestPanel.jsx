import React, { useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import useAxiosSecure from "../../../../hooks/useaxiossecure";
import { FaPlay, FaCheckCircle, FaExclamationTriangle, FaHashtag } from "react-icons/fa";
import { MdBugReport, MdSend } from "react-icons/md";

const SlackTestPanel = () => {
  const [testResults, setTestResults] = useState({});
  const axiosSecure = useAxiosSecure();

  // Fetch channel mappings for testing
  const {
    data: mappingsData,
    isLoading: mappingsLoading
  } = useQuery({
    queryKey: ["slackChannelMappings"],
    queryFn: async () => {
      const response = await axiosSecure.get("/api/integrations/slack/mappings");
      return response.data;
    }
  });

  // Test mapping mutation
  const testMappingMutation = useMutation({
    mutationFn: async ({ channelId, reportType }) => {
      const response = await axiosSecure.post("/api/integrations/slack/test-mapping", {
        channelId,
        reportType
      });
      return response.data;
    },
    onSuccess: (data, variables) => {
      setTestResults(prev => ({
        ...prev,
        [variables.channelId]: {
          success: true,
          message: "Test message sent successfully!",
          timestamp: data.data?.timestamp
        }
      }));
    },
    onError: (error, variables) => {
      setTestResults(prev => ({
        ...prev,
        [variables.channelId]: {
          success: false,
          message: error.response?.data?.error || "Test failed"
        }
      }));
    }
  });

  const handleTestMapping = (mapping) => {
    testMappingMutation.mutate({
      channelId: mapping.slackChannelId,
      reportType: mapping.reportType
    });
  };

  const reportTypes = [
    { value: "daily_work", label: "Daily Work Report" },
    { value: "shopify", label: "Shopify Report" },
    { value: "support", label: "Support Report" },
    { value: "project_update", label: "Project Update" },
    { value: "weekly_summary", label: "Weekly Summary" }
  ];

  if (mappingsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <span className="loading loading-spinner loading-lg"></span>
      </div>
    );
  }

  const mappings = mappingsData?.data || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="overflow-hidden rounded-2xl border border-gray-200 bg-white p-6 dark:border-white/[0.05] dark:bg-white/[0.03]">
        <div className="flex items-center gap-3 mb-6">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-orange-50 dark:bg-orange-900/20">
            <MdBugReport className="text-xl text-orange-600 dark:text-orange-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Test Slack Integration
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Send test messages to verify your channel mappings
            </p>
          </div>
        </div>

        {mappings.length === 0 ? (
          <div className="text-center py-8">
            <MdSend className="mx-auto text-4xl text-gray-400 mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              No channel mappings available for testing
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
              Configure channel mappings first to test Slack connectivity
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                Testing Information
              </h4>
              <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <li>• Test messages will be sent to the configured Slack channels</li>
                <li>• Make sure your bot is invited to the channels you want to test</li>
                <li>• Test messages will create or use existing monthly threads</li>
                <li>• Each test sends a sample report message to verify formatting</li>
              </ul>
            </div>

            {/* Test Mappings */}
            <div className="space-y-3">
              {mappings.map((mapping) => {
                const testResult = testResults[mapping.slackChannelId];
                const isTestingThis = testMappingMutation.isPending && 
                  testMappingMutation.variables?.channelId === mapping.slackChannelId;

                return (
                  <div
                    key={mapping._id}
                    className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full dark:bg-blue-900 dark:text-blue-200">
                          {reportTypes.find(t => t.value === mapping.reportType)?.label || mapping.reportType}
                        </span>
                        <FaHashtag className="text-gray-400" />
                        <span className="font-mono text-sm text-gray-600 dark:text-gray-400">
                          {mapping.slackChannelId}
                        </span>
                        {mapping.slackChannelName && (
                          <span className="text-sm text-gray-500 dark:text-gray-500">
                            ({mapping.slackChannelName})
                          </span>
                        )}
                      </div>

                      {mapping.description && (
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {mapping.description}
                        </p>
                      )}

                      {/* Test Result */}
                      {testResult && (
                        <div className={`mt-2 flex items-center gap-2 text-sm ${
                          testResult.success 
                            ? "text-green-600 dark:text-green-400" 
                            : "text-red-600 dark:text-red-400"
                        }`}>
                          {testResult.success ? (
                            <FaCheckCircle />
                          ) : (
                            <FaExclamationTriangle />
                          )}
                          <span>{testResult.message}</span>
                          {testResult.timestamp && (
                            <span className="text-xs text-gray-500">
                              (Sent at {new Date(testResult.timestamp * 1000).toLocaleTimeString()})
                            </span>
                          )}
                        </div>
                      )}
                    </div>

                    <button
                      onClick={() => handleTestMapping(mapping)}
                      disabled={isTestingThis}
                      className="flex items-center gap-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isTestingThis ? (
                        <span className="loading loading-spinner loading-sm"></span>
                      ) : (
                        <FaPlay />
                      )}
                      {isTestingThis ? "Testing..." : "Test"}
                    </button>
                  </div>
                );
              })}
            </div>

            {/* Test All Button */}
            <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={() => {
                  mappings.forEach(mapping => handleTestMapping(mapping));
                }}
                disabled={testMappingMutation.isPending}
                className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {testMappingMutation.isPending ? (
                  <span className="loading loading-spinner loading-sm"></span>
                ) : (
                  <MdSend />
                )}
                {testMappingMutation.isPending ? "Testing All..." : "Test All Mappings"}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Test Results Summary */}
      {Object.keys(testResults).length > 0 && (
        <div className="overflow-hidden rounded-2xl border border-gray-200 bg-white p-6 dark:border-white/[0.05] dark:bg-white/[0.03]">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-4">
            Test Results Summary
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {Object.values(testResults).filter(r => r.success).length}
              </div>
              <div className="text-sm text-green-700 dark:text-green-300">
                Successful Tests
              </div>
            </div>
            
            <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                {Object.values(testResults).filter(r => !r.success).length}
              </div>
              <div className="text-sm text-red-700 dark:text-red-300">
                Failed Tests
              </div>
            </div>
            
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {Object.keys(testResults).length}
              </div>
              <div className="text-sm text-blue-700 dark:text-blue-300">
                Total Tests
              </div>
            </div>
          </div>

          <button
            onClick={() => setTestResults({})}
            className="mt-4 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
          >
            Clear Results
          </button>
        </div>
      )}
    </div>
  );
};

export default SlackTestPanel;
