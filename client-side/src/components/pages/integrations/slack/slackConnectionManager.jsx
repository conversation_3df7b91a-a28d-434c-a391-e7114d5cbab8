import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import useAxiosSecure from "../../../../hooks/useaxiossecure";
import {
  FaSlack,
  FaEye,
  FaEyeSlash,
  FaCheckCircle,
  FaExclamationTriangle,
} from "react-icons/fa";
import { MdRefresh, MdClose } from "react-icons/md";

const SlackConnectionManager = ({ onStatusChange }) => {
  const [showToken, setShowToken] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const axiosSecure = useAxiosSecure();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm();

  const botToken = watch("botToken");

  // Test connection mutation
  const testConnectionMutation = useMutation({
    mutationFn: async (data) => {
      const response = await axiosSecure.post(
        "/api/integrations/slack/test",
        data
      );
      return response.data;
    },
    onSuccess: (data) => {
      setTestResult({ success: true, data: data.data });
    },
    onError: (error) => {
      setTestResult({
        success: false,
        error: error.response?.data?.error || "Connection test failed",
      });
    },
  });

  // Connect/Update integration mutation
  const connectMutation = useMutation({
    mutationFn: async (data) => {
      const response = await axiosSecure.post(
        "/api/integrations/slack/connect",
        data
      );
      return response.data;
    },
    onSuccess: (data) => {
      reset();
      setTestResult(null);
      onStatusChange?.();
      // Show success message
      alert(data.message || "Slack integration connected successfully!");
    },
    onError: (error) => {
      alert(
        error.response?.data?.error || "Failed to connect Slack integration"
      );
    },
  });

  // Disconnect mutation
  const disconnectMutation = useMutation({
    mutationFn: async () => {
      const response = await axiosSecure.delete(
        "/api/integrations/slack/disconnect"
      );
      return response.data;
    },
    onSuccess: (data) => {
      onStatusChange?.();
      alert(data.message || "Slack integration disconnected successfully!");
    },
    onError: (error) => {
      alert(
        error.response?.data?.error || "Failed to disconnect Slack integration"
      );
    },
  });

  const handleTestConnection = () => {
    if (!botToken) {
      alert("Please enter a bot token first");
      return;
    }
    testConnectionMutation.mutate({ botToken });
  };

  const handleConnect = (data) => {
    connectMutation.mutate(data);
  };

  const handleDisconnect = () => {
    if (
      confirm(
        "Are you sure you want to disconnect the Slack integration? This will stop all automated report posting."
      )
    ) {
      disconnectMutation.mutate();
    }
  };

  return (
    <div className="space-y-6">
      {/* Connection Form */}
      <div className="overflow-hidden rounded-2xl border border-gray-200 bg-white p-6 dark:border-white/[0.05] dark:bg-white/[0.03]">
        <div className="flex items-center gap-3 mb-6">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-50 dark:bg-purple-900/20">
            <FaSlack className="text-xl text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Slack Bot Configuration
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Connect your Slack workspace using a bot token
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit(handleConnect)} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Slack Bot Token
            </label>
            <div className="relative">
              <input
                type={showToken ? "text" : "password"}
                {...register("botToken", {
                  required: "Bot token is required",
                  pattern: {
                    value: /^xoxb-/,
                    message: "Bot token must start with 'xoxb-'",
                  },
                  minLength: {
                    value: 50,
                    message: "Bot token appears to be too short",
                  },
                })}
                className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                placeholder="xoxb-your-bot-token-here"
              />
              <button
                type="button"
                onClick={() => setShowToken(!showToken)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                {showToken ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>
            {errors.botToken && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.botToken.message}
              </p>
            )}
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Get your bot token from your Slack app's "OAuth & Permissions"
              page
            </p>
          </div>

          <div className="flex gap-3">
            <button
              type="button"
              onClick={handleTestConnection}
              disabled={testConnectionMutation.isPending || !botToken}
              className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
            >
              <MdRefresh
                className={
                  testConnectionMutation.isPending ? "animate-spin" : ""
                }
              />
              {testConnectionMutation.isPending
                ? "Testing..."
                : "Test Connection"}
            </button>

            <button
              type="submit"
              disabled={connectMutation.isPending || !botToken}
              className="flex items-center gap-2 px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {connectMutation.isPending ? (
                <span className="loading loading-spinner loading-sm"></span>
              ) : (
                <FaSlack />
              )}
              {connectMutation.isPending ? "Connecting..." : "Connect to Slack"}
            </button>
          </div>
        </form>

        {/* Test Result */}
        {testResult && (
          <div
            className={`mt-4 p-4 rounded-lg ${
              testResult.success
                ? "bg-green-50 border border-green-200 dark:bg-green-900/20 dark:border-green-800"
                : "bg-red-50 border border-red-200 dark:bg-red-900/20 dark:border-red-800"
            }`}
          >
            <div className="flex items-center gap-2 mb-2">
              {testResult.success ? (
                <FaCheckCircle className="text-green-600 dark:text-green-400" />
              ) : (
                <FaExclamationTriangle className="text-red-600 dark:text-red-400" />
              )}
              <span
                className={`font-medium ${
                  testResult.success
                    ? "text-green-800 dark:text-green-200"
                    : "text-red-800 dark:text-red-200"
                }`}
              >
                {testResult.success
                  ? "Connection Successful!"
                  : "Connection Failed"}
              </span>
            </div>

            {testResult.success ? (
              <div className="text-sm text-green-700 dark:text-green-300 space-y-1">
                <p>
                  <strong>Workspace:</strong> {testResult.data.teamName}
                </p>
                <p>
                  <strong>Bot Name:</strong> {testResult.data.botName}
                </p>
                <p>
                  <strong>Team ID:</strong> {testResult.data.teamId}
                </p>
              </div>
            ) : (
              <p className="text-sm text-red-700 dark:text-red-300">
                {testResult.error}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Disconnect Section */}
      <div className="overflow-hidden rounded-2xl border border-red-200 bg-red-50 p-6 dark:border-red-800 dark:bg-red-900/20">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">
              Disconnect Integration
            </h3>
            <p className="text-sm text-red-600 dark:text-red-400">
              This will stop all automated report posting to Slack
            </p>
          </div>
          <button
            onClick={handleDisconnect}
            disabled={disconnectMutation.isPending}
            className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {disconnectMutation.isPending ? (
              <span className="loading loading-spinner loading-sm"></span>
            ) : (
              <MdClose />
            )}
            {disconnectMutation.isPending ? "Disconnecting..." : "Disconnect"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SlackConnectionManager;
