import React, { useState } from "react";
import SlackConnectionManager from "./slackConnectionManager";
import SlackChannelMappings from "./slackChannelMappings";
import SlackTestPanel from "./slackTestPanel";
import { useQuery } from "@tanstack/react-query";
import useAxiosSecure from "../../../../hooks/useaxiossecure";
import { FaSlack, FaCheckCircle, FaExclamationTriangle } from "react-icons/fa";
import { MdSettings, MdMap, MdBugReport } from "react-icons/md";

const SlackIntegration = () => {
  const [activeSection, setActiveSection] = useState("connection");
  const axiosSecure = useAxiosSecure();

  // Fetch Slack integration status
  const {
    data: slackStatus,
    isLoading: statusLoading,
    error: statusError,
    refetch: refetchStatus,
  } = useQuery({
    queryKey: ["slackStatus"],
    queryFn: async () => {
      const response = await axiosSecure.get("/api/integrations/slack/status");
      return response.data;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
    retry: 2,
  });

  const sections = [
    {
      id: "connection",
      name: "Connection",
      icon: <MdSettings className="text-lg" />,
      component: <SlackConnectionManager onStatusChange={refetchStatus} />,
      description: "Manage Slack bot token and connection",
    },
    {
      id: "mappings",
      name: "Channel Mappings",
      icon: <MdMap className="text-lg" />,
      component: <SlackChannelMappings />,
      description: "Configure report type to channel mappings",
    },
    {
      id: "testing",
      name: "Testing",
      icon: <MdBugReport className="text-lg" />,
      component: <SlackTestPanel />,
      description: "Test Slack connectivity and send test messages",
    },
  ];

  if (statusLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <span className="loading loading-spinner loading-lg"></span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Slack Status Overview */}
      <div className="overflow-hidden rounded-2xl border border-gray-200 bg-white p-6 dark:border-white/[0.05] dark:bg-white/[0.03]">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-50 dark:bg-purple-900/20">
              <FaSlack className="text-2xl text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Slack Integration
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Automatically post reports to Slack channels
              </p>
            </div>
          </div>

          {/* Status Indicator */}
          <div className="flex items-center gap-2">
            {statusError ? (
              <>
                <FaExclamationTriangle className="text-red-500" />
                <span className="text-sm font-medium text-red-600 dark:text-red-400">
                  Error Loading Status
                </span>
              </>
            ) : slackStatus?.data?.connected ? (
              <>
                <FaCheckCircle className="text-green-500" />
                <span className="text-sm font-medium text-green-600 dark:text-green-400">
                  Connected
                </span>
              </>
            ) : (
              <>
                <div className="w-3 h-3 rounded-full bg-gray-400"></div>
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Not Connected
                </span>
              </>
            )}
          </div>
        </div>

        {/* Connection Details */}
        {slackStatus?.data?.connected && (
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium text-green-800 dark:text-green-200">
                  Workspace:
                </span>
                <p className="text-green-700 dark:text-green-300">
                  {slackStatus.data.teamName || "Unknown"}
                </p>
              </div>
              <div>
                <span className="font-medium text-green-800 dark:text-green-200">
                  Connected:
                </span>
                <p className="text-green-700 dark:text-green-300">
                  {slackStatus.data.connectedAt
                    ? new Date(
                        slackStatus.data.connectedAt
                      ).toLocaleDateString()
                    : "Unknown"}
                </p>
              </div>
              <div>
                <span className="font-medium text-green-800 dark:text-green-200">
                  Mappings:
                </span>
                <p className="text-green-700 dark:text-green-300">
                  {slackStatus.data.activeMappings || 0} active
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Section Navigation */}
        <div className="mt-6 border-t border-gray-200 dark:border-gray-700 pt-4">
          <nav className="flex space-x-8">
            {sections.map((section) => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeSection === section.id
                    ? "border-purple-500 text-purple-600 dark:text-purple-400"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                {section.icon}
                {section.name}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Active Section Content */}
      <div className="space-y-6">
        {sections.map((section) => (
          <div
            key={section.id}
            className={activeSection === section.id ? "block" : "hidden"}
          >
            {section.component}
          </div>
        ))}
      </div>
    </div>
  );
};

export default SlackIntegration;
