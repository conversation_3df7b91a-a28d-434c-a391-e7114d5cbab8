import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import useAxiosSecure from "../../../../hooks/useaxiossecure";
import { FaPlus, FaTrash, FaEdit, FaHashtag } from "react-icons/fa";
import { MdMap, MdSave, MdCancel } from "react-icons/md";

const SlackChannelMappings = () => {
  const [editingMapping, setEditingMapping] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const axiosSecure = useAxiosSecure();
  const queryClient = useQueryClient();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue
  } = useForm();

  // Fetch channel mappings
  const {
    data: mappingsData,
    isLoading: mappingsLoading,
    error: mappingsError
  } = useQuery({
    queryKey: ["slackChannelMappings"],
    queryFn: async () => {
      const response = await axiosSecure.get("/api/integrations/slack/mappings");
      return response.data;
    },
    retry: 2
  });

  // Create/Update mapping mutation
  const saveMappingMutation = useMutation({
    mutationFn: async (data) => {
      const response = await axiosSecure.post("/api/integrations/slack/mappings", data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["slackChannelMappings"]);
      reset();
      setShowAddForm(false);
      setEditingMapping(null);
    },
    onError: (error) => {
      alert(error.response?.data?.error || "Failed to save channel mapping");
    }
  });

  // Delete mapping mutation
  const deleteMappingMutation = useMutation({
    mutationFn: async (mappingId) => {
      const response = await axiosSecure.delete(`/api/integrations/slack/mappings/${mappingId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["slackChannelMappings"]);
    },
    onError: (error) => {
      alert(error.response?.data?.error || "Failed to delete channel mapping");
    }
  });

  const reportTypes = [
    { value: "daily_work", label: "Daily Work Report" },
    { value: "shopify", label: "Shopify Report" },
    { value: "support", label: "Support Report" },
    { value: "project_update", label: "Project Update" },
    { value: "weekly_summary", label: "Weekly Summary" }
  ];

  const handleSaveMapping = (data) => {
    saveMappingMutation.mutate(data);
  };

  const handleEditMapping = (mapping) => {
    setEditingMapping(mapping._id);
    setValue("reportType", mapping.reportType);
    setValue("slackChannelId", mapping.slackChannelId);
    setValue("description", mapping.description || "");
    setShowAddForm(false);
  };

  const handleCancelEdit = () => {
    setEditingMapping(null);
    reset();
  };

  const handleDeleteMapping = (mappingId) => {
    if (confirm("Are you sure you want to delete this channel mapping?")) {
      deleteMappingMutation.mutate(mappingId);
    }
  };

  const handleAddNew = () => {
    setShowAddForm(true);
    setEditingMapping(null);
    reset();
  };

  if (mappingsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <span className="loading loading-spinner loading-lg"></span>
      </div>
    );
  }

  const mappings = mappingsData?.data || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="overflow-hidden rounded-2xl border border-gray-200 bg-white p-6 dark:border-white/[0.05] dark:bg-white/[0.03]">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-50 dark:bg-blue-900/20">
              <MdMap className="text-xl text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Channel Mappings
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Map report types to Slack channels
              </p>
            </div>
          </div>

          <button
            onClick={handleAddNew}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <FaPlus />
            Add Mapping
          </button>
        </div>

        {/* Add/Edit Form */}
        {(showAddForm || editingMapping) && (
          <form onSubmit={handleSubmit(handleSaveMapping)} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Report Type
                </label>
                <select
                  {...register("reportType", { required: "Report type is required" })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  <option value="">Select report type</option>
                  {reportTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
                {errors.reportType && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.reportType.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Slack Channel ID
                </label>
                <input
                  type="text"
                  {...register("slackChannelId", {
                    required: "Channel ID is required",
                    pattern: {
                      value: /^[CG][A-Z0-9]{8,}$/,
                      message: "Invalid channel ID format (should start with C or G)"
                    }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="C1234567890"
                />
                {errors.slackChannelId && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.slackChannelId.message}
                  </p>
                )}
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Find channel ID in Slack: Right-click channel → View channel details
                </p>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description (Optional)
                </label>
                <input
                  type="text"
                  {...register("description")}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="Brief description of this mapping"
                />
              </div>
            </div>

            <div className="flex gap-3 mt-4">
              <button
                type="submit"
                disabled={saveMappingMutation.isPending}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
              >
                {saveMappingMutation.isPending ? (
                  <span className="loading loading-spinner loading-sm"></span>
                ) : (
                  <MdSave />
                )}
                {saveMappingMutation.isPending ? "Saving..." : "Save Mapping"}
              </button>

              <button
                type="button"
                onClick={() => {
                  setShowAddForm(false);
                  handleCancelEdit();
                }}
                className="flex items-center gap-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
              >
                <MdCancel />
                Cancel
              </button>
            </div>
          </form>
        )}

        {/* Mappings List */}
        {mappingsError ? (
          <div className="text-center py-8">
            <p className="text-red-600 dark:text-red-400">
              Error loading channel mappings: {mappingsError.message}
            </p>
          </div>
        ) : mappings.length === 0 ? (
          <div className="text-center py-8">
            <FaHashtag className="mx-auto text-4xl text-gray-400 mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              No channel mappings configured yet
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
              Add your first mapping to start posting reports to Slack
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {mappings.map((mapping) => (
              <div
                key={mapping._id}
                className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full dark:bg-blue-900 dark:text-blue-200">
                      {reportTypes.find(t => t.value === mapping.reportType)?.label || mapping.reportType}
                    </span>
                    <FaHashtag className="text-gray-400" />
                    <span className="font-mono text-sm text-gray-600 dark:text-gray-400">
                      {mapping.slackChannelId}
                    </span>
                    {mapping.slackChannelName && (
                      <span className="text-sm text-gray-500 dark:text-gray-500">
                        ({mapping.slackChannelName})
                      </span>
                    )}
                  </div>
                  {mapping.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {mapping.description}
                    </p>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleEditMapping(mapping)}
                    className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg dark:text-blue-400 dark:hover:bg-blue-900"
                  >
                    <FaEdit />
                  </button>
                  <button
                    onClick={() => handleDeleteMapping(mapping._id)}
                    disabled={deleteMappingMutation.isPending}
                    className="p-2 text-red-600 hover:bg-red-100 rounded-lg dark:text-red-400 dark:hover:bg-red-900 disabled:opacity-50"
                  >
                    <FaTrash />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SlackChannelMappings;
