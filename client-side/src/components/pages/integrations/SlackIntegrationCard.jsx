import React, { useState } from "react";
import { FaSlack } from "react-icons/fa";
import IntegrationCard from "./IntegrationCard";
import MappingList from "./MappingList";
import MappingModal from "./MappingModal";
import SlackConnectionModal from "./SlackConnectionModal";
import {
  useSlackIntegrationStatus,
  useSlackChannelMappings,
  useSlackMappingTest,
  useSlackMappingMutations,
  getIntegrationStatusInfo,
} from "../../../hooks/useSlackIntegration";

/**
 * Specialized Slack integration card component
 * Compact, self-contained card for dashboard integration
 */
const SlackIntegrationCard = ({ onEdit }) => {
  const [showMappingModal, setShowMappingModal] = useState(false);
  const [editingMapping, setEditingMapping] = useState(null);
  const [showConnectionModal, setShowConnectionModal] = useState(false);

  // Hooks
  const { data: statusData, isLoading: statusLoading } =
    useSlackIntegrationStatus();
  const { data: mappingsData, isLoading: mappingsLoading } =
    useSlackChannelMappings();
  const testMappingMutation = useSlackMappingTest();
  const { saveMutation, deleteMutation } = useSlackMappingMutations();

  // Data processing
  const statusInfo = getIntegrationStatusInfo(statusData);
  const mappings = mappingsData?.data || [];

  // Report types (should ideally come from API)
  const reportTypes = [
    { value: "daily_work", label: "Daily Work Report" },
    { value: "shopify", label: "Shopify Report" },
    { value: "support", label: "Support Report" },
    { value: "project_update", label: "Project Update" },
    { value: "weekly_summary", label: "Weekly Summary" },
  ];

  // Event handlers
  const handleAddMapping = () => {
    setEditingMapping(null);
    setShowMappingModal(true);
  };

  const handleEditMapping = (mapping) => {
    setEditingMapping(mapping);
    setShowMappingModal(true);
  };

  const handleSaveMapping = (mappingData) => {
    saveMutation.mutate(mappingData, {
      onSuccess: () => {
        setShowMappingModal(false);
        setEditingMapping(null);
      },
    });
  };

  const handleDeleteMapping = (mappingId) => {
    deleteMutation.mutate(mappingId);
  };

  const handleTestMapping = (mapping) => {
    testMappingMutation.mutate({
      channelId: mapping.slackChannelId,
      reportType: mapping.reportType,
    });
  };

  const handleQuickTest = () => {
    if (mappings.length > 0) {
      // Test the first mapping as a quick test
      handleTestMapping(mappings[0]);
    }
  };

  const handleEditConnection = () => {
    if (onEdit) {
      onEdit();
    } else {
      setShowConnectionModal(true);
    }
  };

  // Footer content
  const footer = (
    <div className="flex justify-between items-center">
      <div className="text-xs text-gray-400 dark:text-gray-500">
        {statusInfo.lastSync
          ? `Last synced: ${statusInfo.lastSync}`
          : "Never synced"}
      </div>
      {mappings.length > 0 && (
        <button
          onClick={handleQuickTest}
          disabled={testMappingMutation.isPending}
          className="flex items-center gap-1 px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
        >
          {testMappingMutation.isPending ? (
            <span className="loading loading-spinner loading-xs"></span>
          ) : (
            "Send Test"
          )}
        </button>
      )}
    </div>
  );

  if (statusLoading) {
    return (
      <IntegrationCard
        integrationName="Slack Integration"
        icon={FaSlack}
        status="pending"
        subtitle="Loading..."
      >
        <div className="space-y-3">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
      </IntegrationCard>
    );
  }

  return (
    <>
      <IntegrationCard
        integrationName="Slack Integration"
        icon={FaSlack}
        status={statusInfo.status}
        subtitle={statusInfo.subtitle}
        onEdit={handleEditConnection}
        onTest={mappings.length > 0 ? handleQuickTest : undefined}
        footer={footer}
      >
        {/* Mapping List */}
        <MappingList
          mappings={mappings}
          reportTypes={reportTypes}
          onAddMapping={handleAddMapping}
          onEditMapping={handleEditMapping}
          onDeleteMapping={handleDeleteMapping}
          onTestMapping={handleTestMapping}
          isLoading={mappingsLoading}
          maxVisibleMappings={3}
        />

        {/* Connection Status Details */}
        {statusInfo.status === "error" && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg dark:bg-red-900/20 dark:border-red-800">
            <p className="text-sm text-red-700 dark:text-red-300">
              Connection failed. Please check your bot token and try
              reconnecting.
            </p>
          </div>
        )}

        {statusInfo.status === "disconnected" && mappings.length === 0 && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-900/20 dark:border-blue-800">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Connect your Slack workspace to start posting reports
              automatically.
            </p>
          </div>
        )}
      </IntegrationCard>

      {/* Mapping Modal */}
      <MappingModal
        isOpen={showMappingModal}
        onClose={() => {
          setShowMappingModal(false);
          setEditingMapping(null);
        }}
        onSave={handleSaveMapping}
        editingMapping={editingMapping}
        isLoading={saveMutation.isPending}
        reportTypes={reportTypes}
      />

      {/* Connection Modal */}
      <SlackConnectionModal
        isOpen={showConnectionModal}
        onClose={() => setShowConnectionModal(false)}
        currentStatus={statusData?.data}
      />
    </>
  );
};

export default SlackIntegrationCard;
