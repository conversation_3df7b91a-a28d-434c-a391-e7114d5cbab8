import React, { useState } from "react";
import {
  FaSlack,
  FaVideo,
  FaDropbox,
  FaCalendarAlt,
  FaEnvelope,
  FaSearch,
} from "react-icons/fa";
import {
  SiMicrosoftteams,
  SiGooglemeet,
  SiGoogledrive,
  SiOnedrive,
} from "react-icons/si";
import { MdIntegrationInstructions } from "react-icons/md";
import BreadCrumb from "../common/breadcrumb";
import ModernIntegrationCard from "./ModernIntegrationCard";
import IntegrationSettingsModal from "./IntegrationSettingsModal";
import SlackConnectionModal from "./SlackConnectionModal";
import { useSlackIntegrationStatus } from "../../../hooks/useSlackIntegration";
import { showToast } from "../../utils/toasters/toastService";

/**
 * Integration status management hook
 */
const useIntegrationStatus = () => {
  const [integrationStates, setIntegrationStates] = useState({
    zoom: { enabled: false, status: "disconnected" },
    teams: { enabled: true, status: "connected" },
    googlemeet: { enabled: true, status: "connected" },
    googledrive: { enabled: true, status: "connected" },
    dropbox: { enabled: false, status: "disconnected" },
    onedrive: { enabled: false, status: "disconnected" },
    googlecalendar: { enabled: true, status: "connected" },
    outlook: { enabled: false, status: "disconnected" },
    slack: { enabled: true, status: "connected" },
  });

  const toggleIntegration = async (integrationId, enabled) => {
    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setIntegrationStates((prev) => ({
        ...prev,
        [integrationId]: {
          ...prev[integrationId],
          enabled,
          status: enabled ? "connected" : "disconnected",
        },
      }));

      showToast(
        "success",
        `${integrationId} integration ${
          enabled ? "enabled" : "disabled"
        } successfully!`
      );
    } catch (error) {
      showToast(
        "error",
        `Failed to ${
          enabled ? "enable" : "disable"
        } ${integrationId} integration`
      );
    }
  };

  return { integrationStates, toggleIntegration };
};

/**
 * Modern integrations page matching the screenshot design
 */
const ModernIntegrationsPage = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedIntegration, setSelectedIntegration] = useState(null);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [showSlackConfig, setShowSlackConfig] = useState(false);

  const { integrationStates, toggleIntegration } = useIntegrationStatus();
  const { data: slackStatus } = useSlackIntegrationStatus();

  // Available integrations matching the screenshot
  const integrations = [
    {
      id: "zoom",
      name: "Zoom",
      icon: FaVideo,
      description: "AI-powered meeting transcriptions & summaries.",
      features: [
        "Automatic meeting transcription",
        "AI-generated summaries",
        "Action item extraction",
        "Meeting highlights",
      ],
    },
    {
      id: "teams",
      name: "Microsoft Teams",
      icon: SiMicrosoftteams,
      description: "Capture key discussions & automate action items.",
      features: [
        "Meeting recording integration",
        "Chat message analysis",
        "Task automation",
        "Team collaboration insights",
      ],
    },
    {
      id: "googlemeet",
      name: "Google Meet",
      icon: SiGooglemeet,
      description: "Sync meeting notes with AI-generated highlights.",
      features: [
        "Real-time transcription",
        "Meeting summaries",
        "Calendar integration",
        "Participant insights",
      ],
    },
    {
      id: "googledrive",
      name: "Google Drive",
      icon: SiGoogledrive,
      description: "Save meeting notes and attachments securely.",
      features: [
        "Automatic file backup",
        "Document collaboration",
        "Version control",
        "Secure sharing",
      ],
    },
    {
      id: "dropbox",
      name: "Dropbox",
      icon: FaDropbox,
      description: "Store and share files directly from your meetings.",
      features: [
        "File synchronization",
        "Team folders",
        "Version history",
        "Advanced sharing controls",
      ],
    },
    {
      id: "onedrive",
      name: "OneDrive",
      icon: SiOnedrive,
      description: "Sync meeting documents across your devices.",
      features: [
        "Cross-device sync",
        "Office integration",
        "Real-time collaboration",
        "Advanced security",
      ],
    },
    {
      id: "googlecalendar",
      name: "Google Calendar",
      icon: FaCalendarAlt,
      description: "Auto-sync meetings & set reminders.",
      features: [
        "Meeting scheduling",
        "Automatic reminders",
        "Calendar integration",
        "Time zone management",
      ],
    },
    {
      id: "outlook",
      name: "Microsoft Outlook",
      icon: FaEnvelope,
      description: "Schedule and track discussions effortlessly.",
      features: [
        "Email integration",
        "Meeting scheduling",
        "Contact management",
        "Task tracking",
      ],
    },
    {
      id: "slack",
      name: "Slack",
      icon: FaSlack,
      description:
        "Get instant meeting summaries & action items in your team channels.",
      features: [
        "Channel notifications",
        "Meeting summaries",
        "Action item tracking",
        "Team collaboration",
      ],
      onConfigure: () => setShowSlackConfig(true),
    },
  ];

  const filteredIntegrations = integrations.filter((integration) =>
    integration.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleToggleIntegration = async (integrationId, enabled) => {
    await toggleIntegration(integrationId, enabled);
  };

  const handleShowSettings = (integration) => {
    const state = integrationStates[integration.id];
    setSelectedIntegration({
      ...integration,
      isEnabled: state.enabled,
      status: state.status,
      lastSync: state.enabled ? new Date().toISOString() : null,
    });
    setShowSettingsModal(true);
  };

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <BreadCrumb
        title="Integrations"
        icon={<MdIntegrationInstructions className="text-xl" />}
        description="Connect your workspace with external services"
      />

      {/* Header with Search */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Integrations
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Connect and manage your external service integrations
            </p>
          </div>

          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaSearch className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search integrations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
            />
          </div>
        </div>
      </div>

      {/* Integration Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredIntegrations.map((integration) => {
          const state = integrationStates[integration.id];
          return (
            <ModernIntegrationCard
              key={integration.id}
              icon={integration.icon}
              name={integration.name}
              description={integration.description}
              isEnabled={state.enabled}
              onToggle={(enabled) =>
                handleToggleIntegration(integration.id, enabled)
              }
              onSettings={() => handleShowSettings(integration)}
            />
          );
        })}
      </div>

      {/* Settings Modal */}
      <IntegrationSettingsModal
        isOpen={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
        integration={selectedIntegration}
      />

      {/* Slack Configuration Modal */}
      <SlackConnectionModal
        isOpen={showSlackConfig}
        onClose={() => setShowSlackConfig(false)}
        currentStatus={slackStatus?.data}
      />
    </div>
  );
};

export default ModernIntegrationsPage;
