import React from "react";
import { useForm } from "react-hook-form";
import { useMutation } from "@tanstack/react-query";
import { FaTimes } from "react-icons/fa";
import useAxiosSecure from "../../../hooks/useaxiossecure";
import { showToast } from "../toasters/toastService";

const ReportTypeModal = ({ reportType, onClose, onSuccess }) => {
  const axiosSecure = useAxiosSecure();
  const isEditing = !!reportType;

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      key: reportType?.key || "",
      name: reportType?.name || "",
      description: reportType?.description || "",
      isActive: reportType?.isActive !== undefined ? reportType.isActive : true,
      sortOrder: reportType?.sortOrder || 0,
    },
  });

  // Create/Update mutation
  const mutation = useMutation({
    mutationFn: async (data) => {
      if (isEditing) {
        const response = await axiosSecure.put(`/api/report-types/${reportType.key}`, data);
        return response.data;
      } else {
        const response = await axiosSecure.post("/api/report-types", data);
        return response.data;
      }
    },
    onSuccess: () => {
      showToast("success", `Report type ${isEditing ? "updated" : "created"} successfully`);
      onSuccess();
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.error || `Failed to ${isEditing ? "update" : "create"} report type`;
      showToast("error", errorMessage);
    },
  });

  const onSubmit = (data) => {
    mutation.mutate(data);
  };

  return (
    <>
      <div className="fixed inset-0 h-full w-full bg-gray-400/60 z-40"></div>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md">
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {isEditing ? "Edit Report Type" : "Add Report Type"}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <FaTimes />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-4">
            {/* Key */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Key <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                {...register("key", {
                  required: "Key is required",
                  pattern: {
                    value: /^[a-z0-9_]+$/,
                    message: "Key must contain only lowercase letters, numbers, and underscores",
                  },
                })}
                disabled={isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white disabled:opacity-50"
                placeholder="e.g., daily_work"
              />
              {errors.key && (
                <p className="text-red-500 text-sm mt-1">{errors.key.message}</p>
              )}
            </div>

            {/* Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                {...register("name", { required: "Name is required" })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="e.g., Daily Work Report"
              />
              {errors.name && (
                <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
              )}
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <textarea
                {...register("description")}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="Brief description of this report type"
              />
            </div>

            {/* Sort Order */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Sort Order
              </label>
              <input
                type="number"
                {...register("sortOrder", { valueAsNumber: true })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="0"
              />
            </div>

            {/* Active Status */}
            <div>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  {...register("isActive")}
                  className="rounded"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  Active (available for selection)
                </span>
              </label>
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={mutation.isPending}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {mutation.isPending
                  ? isEditing
                    ? "Updating..."
                    : "Creating..."
                  : isEditing
                  ? "Update"
                  : "Create"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default ReportTypeModal;
