import React from "react";
import { FaSlack, FaExternalLinkAlt } from "react-icons/fa";

const SlackThreadLink = ({ threadUrl, channelId, posted = false, error = null }) => {
  if (!posted && !error) {
    return null; // Don't show anything if no Slack interaction
  }

  if (error) {
    return (
      <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg dark:bg-red-900/20 dark:border-red-800">
        <div className="flex items-center gap-2">
          <FaSlack className="text-red-600 dark:text-red-400" />
          <span className="text-sm font-medium text-red-800 dark:text-red-200">
            Slack Posting Failed
          </span>
        </div>
        <p className="text-sm text-red-700 dark:text-red-300 mt-1">
          {error}
        </p>
      </div>
    );
  }

  if (posted && threadUrl) {
    return (
      <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg dark:bg-green-900/20 dark:border-green-800">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FaSlack className="text-green-600 dark:text-green-400" />
            <span className="text-sm font-medium text-green-800 dark:text-green-200">
              Posted to Slack
            </span>
          </div>
          <a
            href={threadUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-1 text-sm text-green-700 hover:text-green-800 dark:text-green-300 dark:hover:text-green-200"
          >
            View Thread
            <FaExternalLinkAlt className="text-xs" />
          </a>
        </div>
        {channelId && (
          <p className="text-xs text-green-600 dark:text-green-400 mt-1">
            Channel: {channelId}
          </p>
        )}
      </div>
    );
  }

  return null;
};

export default SlackThreadLink;
