@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@100..900&display=swap");

/* Custom CSS Variables */
:root {
  --font-outfit: 'Outfit', sans-serif;
}

/* Custom styles for the application */
body {
  font-family: var(--font-outfit);
}

/* Custom component styles */
.menu-item {
  @apply flex items-center gap-3 rounded-lg w-full px-3 py-2 text-sm font-medium relative;
}

.menu-dropdown-item {
  @apply flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium relative;
}

.menu-dropdown-badge {
  @apply block px-2.5 py-0.5 text-xs font-medium text-blue-600 uppercase rounded-full;
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  border-radius: 9999px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #e5e7eb;
  border-radius: 9999px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #d1d5db;
}

.menu-item-icon-size svg {
  width: 24px !important;
  height: 24px !important;
}
