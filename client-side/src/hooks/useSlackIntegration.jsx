import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import useAxiosSecure from "./useaxiossecure";
import { showToast } from "../components/utils/toasters/toastService";

/**
 * Hook for managing Slack integration status
 */
export const useSlackIntegrationStatus = () => {
  const axiosSecure = useAxiosSecure();

  return useQuery({
    queryKey: ["slackIntegrationStatus"],
    queryFn: async () => {
      const response = await axiosSecure.get("/api/integrations/slack/status");
      return response.data;
    },
    retry: 2,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false
  });
};

/**
 * Hook for managing Slack channel mappings
 */
export const useSlackChannelMappings = () => {
  const axiosSecure = useAxiosSecure();

  return useQuery({
    queryKey: ["slackChannelMappings"],
    queryFn: async () => {
      const response = await axiosSecure.get("/api/integrations/slack/mappings");
      return response.data;
    },
    retry: 2,
    staleTime: 60000, // 1 minute
    refetchOnWindowFocus: false
  });
};

/**
 * Hook for testing Slack connection
 */
export const useSlackConnectionTest = () => {
  const axiosSecure = useAxiosSecure();

  return useMutation({
    mutationFn: async ({ botToken }) => {
      const response = await axiosSecure.post("/api/integrations/slack/test-connection", {
        botToken
      });
      return response.data;
    },
    onSuccess: (data) => {
      showToast("success", "Connection test successful!", { icon: "✅" });
    },
    onError: (error) => {
      const message = error.response?.data?.error || "Connection test failed";
      showToast("error", message, { icon: "❌" });
    }
  });
};

/**
 * Hook for testing individual channel mappings
 */
export const useSlackMappingTest = () => {
  const axiosSecure = useAxiosSecure();

  return useMutation({
    mutationFn: async ({ channelId, reportType }) => {
      const response = await axiosSecure.post("/api/integrations/slack/test-mapping", {
        channelId,
        reportType
      });
      return response.data;
    },
    onSuccess: (data, variables) => {
      showToast("success", `Test message sent to channel successfully!`, { icon: "📤" });
    },
    onError: (error, variables) => {
      const message = error.response?.data?.error || "Test message failed";
      showToast("error", `Test failed: ${message}`, { icon: "❌" });
    }
  });
};

/**
 * Hook for connecting/updating Slack integration
 */
export const useSlackConnect = () => {
  const axiosSecure = useAxiosSecure();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ botToken }) => {
      const response = await axiosSecure.post("/api/integrations/slack/connect", {
        botToken
      });
      return response.data;
    },
    onSuccess: (data) => {
      // Invalidate and refetch integration status
      queryClient.invalidateQueries(["slackIntegrationStatus"]);
      showToast("success", "Slack integration connected successfully!", { icon: "🎉" });
    },
    onError: (error) => {
      const message = error.response?.data?.error || "Failed to connect Slack integration";
      showToast("error", message, { icon: "❌" });
    }
  });
};

/**
 * Hook for disconnecting Slack integration
 */
export const useSlackDisconnect = () => {
  const axiosSecure = useAxiosSecure();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const response = await axiosSecure.delete("/api/integrations/slack/disconnect");
      return response.data;
    },
    onSuccess: (data) => {
      // Invalidate and refetch integration status
      queryClient.invalidateQueries(["slackIntegrationStatus"]);
      queryClient.invalidateQueries(["slackChannelMappings"]);
      showToast("success", "Slack integration disconnected successfully!", { icon: "👋" });
    },
    onError: (error) => {
      const message = error.response?.data?.error || "Failed to disconnect Slack integration";
      showToast("error", message, { icon: "❌" });
    }
  });
};

/**
 * Hook for managing channel mappings (create/update/delete)
 */
export const useSlackMappingMutations = () => {
  const axiosSecure = useAxiosSecure();
  const queryClient = useQueryClient();

  const saveMutation = useMutation({
    mutationFn: async (mappingData) => {
      const response = await axiosSecure.post("/api/integrations/slack/mappings", mappingData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["slackChannelMappings"]);
      showToast("success", "Channel mapping saved successfully!", { icon: "💾" });
    },
    onError: (error) => {
      const message = error.response?.data?.error || "Failed to save channel mapping";
      showToast("error", message, { icon: "❌" });
    }
  });

  const deleteMutation = useMutation({
    mutationFn: async (mappingId) => {
      const response = await axiosSecure.delete(`/api/integrations/slack/mappings/${mappingId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["slackChannelMappings"]);
      showToast("success", "Channel mapping deleted successfully!", { icon: "🗑️" });
    },
    onError: (error) => {
      const message = error.response?.data?.error || "Failed to delete channel mapping";
      showToast("error", message, { icon: "❌" });
    }
  });

  return {
    saveMutation,
    deleteMutation
  };
};

/**
 * Utility function to get integration status display info
 */
export const getIntegrationStatusInfo = (statusData) => {
  if (!statusData?.data) {
    return {
      status: "disconnected",
      subtitle: "Not configured",
      lastSync: null
    };
  }

  const { connected, teamName, lastTestAt, connectionStatus } = statusData.data;

  if (connected && connectionStatus === "connected") {
    return {
      status: "connected",
      subtitle: teamName ? `Workspace: ${teamName}` : "Connected",
      lastSync: lastTestAt ? new Date(lastTestAt).toLocaleString() : null
    };
  }

  if (connectionStatus === "failed") {
    return {
      status: "error",
      subtitle: "Connection failed",
      lastSync: null
    };
  }

  if (connectionStatus === "pending") {
    return {
      status: "pending",
      subtitle: "Testing connection...",
      lastSync: null
    };
  }

  return {
    status: "disconnected",
    subtitle: "Not connected",
    lastSync: null
  };
};
