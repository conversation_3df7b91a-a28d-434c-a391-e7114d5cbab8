{"name": "client-side", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@tanstack/react-query": "^5.74.4", "apexcharts": "^4.5.0", "axios": "^1.9.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "firebase": "^11.6.1", "framer-motion": "^12.7.4", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-confirm-alert": "^3.0.6", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.0", "react-icons": "^5.5.0", "react-router": "^7.5.0", "react-toastify": "^11.0.5", "undefined": "^0.1.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.0.4", "autoprefixer": "^10.4.21", "daisyui": "^5.0.19", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^4.5.3"}}