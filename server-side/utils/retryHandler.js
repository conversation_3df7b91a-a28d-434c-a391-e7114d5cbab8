/**
 * Retry Handler Utility
 * Provides exponential backoff and retry logic for API calls
 */
class RetryHandler {
  constructor(options = {}) {
    this.maxRetries = options.maxRetries || 3;
    this.baseDelay = options.baseDelay || 1000; // 1 second
    this.maxDelay = options.maxDelay || 30000; // 30 seconds
    this.backoffFactor = options.backoffFactor || 2;
    this.jitter = options.jitter !== false; // Add jitter by default
  }

  /**
   * Execute a function with retry logic
   * @param {Function} fn - Function to execute
   * @param {Object} options - Retry options
   * @returns {Promise<any>} Function result
   */
  async execute(fn, options = {}) {
    const maxRetries = options.maxRetries || this.maxRetries;
    const retryableErrors = options.retryableErrors || ['rate_limited', 'timeout', 'network_error'];
    
    let lastError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const result = await fn();
        return result;
      } catch (error) {
        lastError = error;
        
        // Don't retry on the last attempt
        if (attempt === maxRetries) {
          break;
        }
        
        // Check if error is retryable
        if (!this.isRetryableError(error, retryableErrors)) {
          throw error;
        }
        
        // Calculate delay
        const delay = this.calculateDelay(attempt, error);
        
        console.log(`Attempt ${attempt + 1} failed: ${error.message}. Retrying in ${delay}ms...`);
        
        // Wait before retrying
        await this.sleep(delay);
      }
    }
    
    throw lastError;
  }

  /**
   * Check if an error is retryable
   * @param {Error} error - Error to check
   * @param {Array} retryableErrors - List of retryable error codes
   * @returns {boolean} Whether the error is retryable
   */
  isRetryableError(error, retryableErrors) {
    // Check error code
    if (error.code && retryableErrors.includes(error.code)) {
      return true;
    }
    
    // Check error message for common retryable patterns
    const retryablePatterns = [
      /rate.?limit/i,
      /timeout/i,
      /network/i,
      /connection/i,
      /temporary/i,
      /unavailable/i,
      /server.?error/i
    ];
    
    return retryablePatterns.some(pattern => pattern.test(error.message));
  }

  /**
   * Calculate delay for next retry
   * @param {number} attempt - Current attempt number (0-based)
   * @param {Error} error - Error that occurred
   * @returns {number} Delay in milliseconds
   */
  calculateDelay(attempt, error) {
    let delay;
    
    // Handle rate limiting with specific retry-after header
    if (error.code === 'rate_limited' && error.headers && error.headers['retry-after']) {
      delay = parseInt(error.headers['retry-after']) * 1000;
    } else {
      // Exponential backoff
      delay = Math.min(
        this.baseDelay * Math.pow(this.backoffFactor, attempt),
        this.maxDelay
      );
    }
    
    // Add jitter to prevent thundering herd
    if (this.jitter) {
      delay = delay + (Math.random() * 0.1 * delay);
    }
    
    return Math.floor(delay);
  }

  /**
   * Sleep utility
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Slack-specific retry handler
 */
class SlackRetryHandler extends RetryHandler {
  constructor(options = {}) {
    super({
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 60000, // 1 minute max for Slack
      backoffFactor: 2,
      ...options
    });
  }

  /**
   * Check if a Slack error is retryable
   * @param {Error} error - Slack API error
   * @param {Array} retryableErrors - Additional retryable error codes
   * @returns {boolean} Whether the error is retryable
   */
  isRetryableError(error, retryableErrors = []) {
    const slackRetryableErrors = [
      'rate_limited',
      'timeout',
      'server_error',
      'service_unavailable',
      'internal_error',
      'fatal_error'
    ];
    
    const allRetryableErrors = [...slackRetryableErrors, ...retryableErrors];
    
    return super.isRetryableError(error, allRetryableErrors);
  }

  /**
   * Execute Slack API call with retry logic
   * @param {Function} slackApiCall - Slack API call function
   * @param {Object} options - Retry options
   * @returns {Promise<any>} API call result
   */
  async executeSlackCall(slackApiCall, options = {}) {
    return this.execute(async () => {
      try {
        const result = await slackApiCall();
        
        // Check if Slack returned an error in the response
        if (result && !result.ok && result.error) {
          const error = new Error(result.error);
          error.code = result.error;
          throw error;
        }
        
        return result;
      } catch (error) {
        // Handle Slack Web API errors
        if (error.code === 'rate_limited') {
          // Add retry-after header info if available
          if (error.headers) {
            error.headers = error.headers;
          }
        }
        
        throw error;
      }
    }, options);
  }
}

/**
 * Rate limiter for API calls
 */
class RateLimiter {
  constructor(options = {}) {
    this.requestsPerWindow = options.requestsPerWindow || 50;
    this.windowMs = options.windowMs || 60000; // 1 minute
    this.requests = [];
  }

  /**
   * Check if request is allowed
   * @returns {boolean} Whether request is allowed
   */
  isAllowed() {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    
    // Remove old requests
    this.requests = this.requests.filter(timestamp => timestamp > windowStart);
    
    // Check if we're under the limit
    return this.requests.length < this.requestsPerWindow;
  }

  /**
   * Record a request
   */
  recordRequest() {
    this.requests.push(Date.now());
  }

  /**
   * Get time until next request is allowed
   * @returns {number} Milliseconds until next request
   */
  getTimeUntilReset() {
    if (this.requests.length === 0) {
      return 0;
    }
    
    const oldestRequest = Math.min(...this.requests);
    const resetTime = oldestRequest + this.windowMs;
    const now = Date.now();
    
    return Math.max(0, resetTime - now);
  }

  /**
   * Wait until request is allowed
   * @returns {Promise<void>}
   */
  async waitForAllowance() {
    if (this.isAllowed()) {
      this.recordRequest();
      return;
    }
    
    const waitTime = this.getTimeUntilReset();
    console.log(`Rate limit reached. Waiting ${waitTime}ms...`);
    
    await new Promise(resolve => setTimeout(resolve, waitTime));
    this.recordRequest();
  }
}

module.exports = {
  RetryHandler,
  SlackRetryHandler,
  RateLimiter
};
