const SlackIntegration = require("../models/slackIntegration");
const ChannelMapping = require("../models/channelMapping");
const ThreadTracker = require("../models/threadTracker");
const ReportType = require("../models/reportType");
const WorkReport = require("../models/workReport");

/**
 * Database initialization utility
 * Creates collections and indexes for Slack integration
 */
class DatabaseInit {
  constructor(db) {
    this.db = db;
  }

  /**
   * Initialize all Slack-related collections and indexes
   */
  async initializeSlackCollections() {
    console.log("Initializing Slack integration collections...");

    try {
      // Create collections if they don't exist
      await this.createCollections();

      // Create indexes
      await this.createIndexes();

      // Initialize default report types
      await this.initializeDefaultReportTypes();

      console.log("Slack integration collections initialized successfully");
    } catch (error) {
      console.error("Error initializing Slack collections:", error);
      throw error;
    }
  }

  /**
   * Create collections if they don't exist
   */
  async createCollections() {
    const collections = [
      "slack_integrations",
      "slack_channel_mappings",
      "slack_thread_trackers",
      "reportTypes",
    ];

    for (const collectionName of collections) {
      try {
        await this.db.createCollection(collectionName);
        console.log(`Created collection: ${collectionName}`);
      } catch (error) {
        if (error.code === 48) {
          console.log(`Collection ${collectionName} already exists`);
        } else {
          throw error;
        }
      }
    }
  }

  /**
   * Create all required indexes
   */
  async createIndexes() {
    await this.createSlackIntegrationIndexes();
    await this.createChannelMappingIndexes();
    await this.createThreadTrackerIndexes();
    await this.createWorkReportIndexes();
  }

  /**
   * Create SlackIntegration collection indexes
   */
  async createSlackIntegrationIndexes() {
    const collection = this.db.collection("slack_integrations");
    const indexes = SlackIntegration.getIndexes();

    for (const index of indexes) {
      try {
        await collection.createIndex(index.key, {
          unique: index.unique || false,
          background: true,
        });
        console.log(`Created SlackIntegration index:`, index.key);
      } catch (error) {
        if (error.code === 85) {
          console.log(`SlackIntegration index already exists:`, index.key);
        } else {
          console.error(`Error creating SlackIntegration index:`, error);
        }
      }
    }
  }

  /**
   * Create ChannelMapping collection indexes
   */
  async createChannelMappingIndexes() {
    const collection = this.db.collection("slack_channel_mappings");
    const indexes = ChannelMapping.getIndexes();

    for (const index of indexes) {
      try {
        await collection.createIndex(index.key, {
          unique: index.unique || false,
          background: true,
        });
        console.log(`Created ChannelMapping index:`, index.key);
      } catch (error) {
        if (error.code === 85) {
          console.log(`ChannelMapping index already exists:`, index.key);
        } else {
          console.error(`Error creating ChannelMapping index:`, error);
        }
      }
    }
  }

  /**
   * Create ThreadTracker collection indexes
   */
  async createThreadTrackerIndexes() {
    const collection = this.db.collection("slack_thread_trackers");
    const indexes = ThreadTracker.getIndexes();

    for (const index of indexes) {
      try {
        await collection.createIndex(index.key, {
          unique: index.unique || false,
          background: true,
        });
        console.log(`Created ThreadTracker index:`, index.key);
      } catch (error) {
        if (error.code === 85) {
          console.log(`ThreadTracker index already exists:`, index.key);
        } else {
          console.error(`Error creating ThreadTracker index:`, error);
        }
      }
    }
  }

  /**
   * Create WorkReport collection indexes (extend existing reports collection)
   */
  async createWorkReportIndexes() {
    const collection = this.db.collection("reports");
    const indexes = WorkReport.getIndexes();

    for (const index of indexes) {
      try {
        // Skip the existing unique index on email and report_date
        if (index.key.email && index.key.report_date && index.unique) {
          console.log(`Skipping existing WorkReport index:`, index.key);
          continue;
        }

        await collection.createIndex(index.key, {
          unique: index.unique || false,
          background: true,
        });
        console.log(`Created WorkReport index:`, index.key);
      } catch (error) {
        if (error.code === 85) {
          console.log(`WorkReport index already exists:`, index.key);
        } else {
          console.error(`Error creating WorkReport index:`, error);
        }
      }
    }
  }

  /**
   * Drop all Slack-related collections (for cleanup/reset)
   */
  async dropSlackCollections() {
    console.log("Dropping Slack integration collections...");

    const collections = [
      "slack_integrations",
      "slack_channel_mappings",
      "slack_thread_trackers",
    ];

    for (const collectionName of collections) {
      try {
        await this.db.collection(collectionName).drop();
        console.log(`Dropped collection: ${collectionName}`);
      } catch (error) {
        if (error.code === 26) {
          console.log(`Collection ${collectionName} does not exist`);
        } else {
          console.error(`Error dropping collection ${collectionName}:`, error);
        }
      }
    }
  }

  /**
   * Get collection statistics
   */
  async getCollectionStats() {
    const collections = [
      "slack_integrations",
      "slack_channel_mappings",
      "slack_thread_trackers",
      "reports",
    ];

    const stats = {};

    for (const collectionName of collections) {
      try {
        const collection = this.db.collection(collectionName);
        const count = await collection.countDocuments();
        const indexes = await collection.indexes();

        stats[collectionName] = {
          documentCount: count,
          indexCount: indexes.length,
          indexes: indexes.map((idx) => idx.key),
        };
      } catch (error) {
        stats[collectionName] = { error: error.message };
      }
    }

    return stats;
  }

  /**
   * Initialize default report types
   */
  async initializeDefaultReportTypes() {
    console.log("Initializing default report types...");

    try {
      const reportTypesCollection = this.db.collection("reportTypes");
      const defaultReportTypes = ReportType.getDefaults();

      for (const reportType of defaultReportTypes) {
        // Check if already exists
        const existing = await reportTypesCollection.findOne({
          key: reportType.key,
        });
        if (!existing) {
          await reportTypesCollection.insertOne(reportType.toObject());
          console.log(`Created report type: ${reportType.key}`);
        } else {
          console.log(`Report type already exists: ${reportType.key}`);
        }
      }

      console.log("Default report types initialized successfully");
    } catch (error) {
      console.error("Error initializing default report types:", error);
      throw error;
    }
  }
}

module.exports = DatabaseInit;
