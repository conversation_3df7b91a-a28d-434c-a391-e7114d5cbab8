const express = require('express');
const { ObjectId } = require('mongodb');
const ReportType = require('../models/reportType');

/**
 * Create report types routes
 * @param {Object} db - MongoDB database instance
 * @returns {express.Router} Express router
 */
function createReportTypesRoutes(db) {
  const router = express.Router();
  const reportTypesCollection = db.collection('reportTypes');

  /**
   * Get all report types
   * GET /api/report-types
   */
  router.get('/', async (req, res) => {
    try {
      const { includeInactive = false } = req.query;
      
      const query = includeInactive === 'true' ? {} : { isActive: true };
      const reportTypes = await reportTypesCollection
        .find(query)
        .sort({ sortOrder: 1, name: 1 })
        .toArray();

      res.json({
        success: true,
        data: reportTypes
      });
    } catch (error) {
      console.error('Error fetching report types:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch report types'
      });
    }
  });

  /**
   * Get single report type by key
   * GET /api/report-types/:key
   */
  router.get('/:key', async (req, res) => {
    try {
      const { key } = req.params;
      const reportType = await reportTypesCollection.findOne({ key });

      if (!reportType) {
        return res.status(404).json({
          success: false,
          error: 'Report type not found'
        });
      }

      res.json({
        success: true,
        data: reportType
      });
    } catch (error) {
      console.error('Error fetching report type:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch report type'
      });
    }
  });

  /**
   * Create new report type
   * POST /api/report-types
   * Requires admin privileges
   */
  router.post('/', async (req, res) => {
    try {
      const reportTypeData = req.body;
      const reportType = new ReportType(reportTypeData);

      // Validate report type
      const validation = reportType.validate();
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: validation.errors
        });
      }

      // Check if key already exists
      const existingReportType = await reportTypesCollection.findOne({ key: reportType.key });
      if (existingReportType) {
        return res.status(409).json({
          success: false,
          error: 'Report type with this key already exists'
        });
      }

      // Insert report type
      const result = await reportTypesCollection.insertOne(reportType.toObject());

      res.status(201).json({
        success: true,
        data: {
          ...reportType.toObject(),
          _id: result.insertedId
        }
      });
    } catch (error) {
      console.error('Error creating report type:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create report type'
      });
    }
  });

  /**
   * Update report type
   * PUT /api/report-types/:key
   * Requires admin privileges
   */
  router.put('/:key', async (req, res) => {
    try {
      const { key } = req.params;
      const updateData = req.body;

      // Don't allow key changes
      delete updateData.key;
      updateData.updatedAt = new Date();

      // Validate updated data
      const existingReportType = await reportTypesCollection.findOne({ key });
      if (!existingReportType) {
        return res.status(404).json({
          success: false,
          error: 'Report type not found'
        });
      }

      const updatedReportType = new ReportType({ ...existingReportType, ...updateData });
      const validation = updatedReportType.validate();
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: validation.errors
        });
      }

      // Update report type
      const result = await reportTypesCollection.updateOne(
        { key },
        { $set: updateData }
      );

      if (result.matchedCount === 0) {
        return res.status(404).json({
          success: false,
          error: 'Report type not found'
        });
      }

      // Fetch updated document
      const updated = await reportTypesCollection.findOne({ key });

      res.json({
        success: true,
        data: updated
      });
    } catch (error) {
      console.error('Error updating report type:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update report type'
      });
    }
  });

  /**
   * Delete report type (soft delete by setting isActive to false)
   * DELETE /api/report-types/:key
   * Requires admin privileges
   */
  router.delete('/:key', async (req, res) => {
    try {
      const { key } = req.params;

      const result = await reportTypesCollection.updateOne(
        { key },
        { 
          $set: { 
            isActive: false,
            updatedAt: new Date()
          }
        }
      );

      if (result.matchedCount === 0) {
        return res.status(404).json({
          success: false,
          error: 'Report type not found'
        });
      }

      res.json({
        success: true,
        message: 'Report type deactivated successfully'
      });
    } catch (error) {
      console.error('Error deleting report type:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete report type'
      });
    }
  });

  /**
   * Initialize default report types
   * POST /api/report-types/initialize
   * Requires admin privileges
   */
  router.post('/initialize', async (req, res) => {
    try {
      const defaultReportTypes = ReportType.getDefaults();
      const results = [];

      for (const reportType of defaultReportTypes) {
        // Check if already exists
        const existing = await reportTypesCollection.findOne({ key: reportType.key });
        if (!existing) {
          const result = await reportTypesCollection.insertOne(reportType.toObject());
          results.push({
            key: reportType.key,
            action: 'created',
            id: result.insertedId
          });
        } else {
          results.push({
            key: reportType.key,
            action: 'already_exists',
            id: existing._id
          });
        }
      }

      res.json({
        success: true,
        message: 'Default report types initialized',
        results
      });
    } catch (error) {
      console.error('Error initializing report types:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to initialize report types'
      });
    }
  });

  return router;
}

module.exports = createReportTypesRoutes;
