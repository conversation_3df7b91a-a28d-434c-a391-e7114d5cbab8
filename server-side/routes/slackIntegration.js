const express = require("express");
const { ObjectId } = require("mongodb");
const SlackIntegrationService = require("../services/slackIntegrationService");
const { verifyToken, verifyAdmin } = require("../middleware/auth");

const router = express.Router();

/**
 * Initialize Slack integration routes
 * @param {Object} db - MongoDB database instance
 * @returns {Router} Express router
 */
function createSlackIntegrationRoutes(db) {
  const slackService = new SlackIntegrationService(db);

  /**
   * GET /api/integrations/slack/status
   * Get current Slack integration status
   */
  router.get("/status", verifyToken, verifyAdmin(db), async (req, res) => {
    try {
      const status = await slackService.getIntegrationStatus();
      res.json({
        success: true,
        data: status,
      });
    } catch (error) {
      console.error("Error getting Slack integration status:", error);
      res.status(500).json({
        success: false,
        error: "Failed to get integration status",
      });
    }
  });

  /**
   * POST /api/integrations/slack/connect
   * Connect or update Slack integration
   */
  router.post("/connect", verifyToken, verifyAdmin(db), async (req, res) => {
    try {
      const { botToken } = req.body;

      if (!botToken || typeof botToken !== "string") {
        return res.status(400).json({
          success: false,
          error: "Bot token is required and must be a string",
        });
      }

      // Validate bot token format
      if (!botToken.startsWith("xoxb-")) {
        return res.status(400).json({
          success: false,
          error:
            'Invalid bot token format. Bot tokens should start with "xoxb-"',
        });
      }

      const result = await slackService.createOrUpdateIntegration(
        botToken,
        req.user.userId
      );

      if (!result.success) {
        return res.status(400).json({
          success: false,
          error: result.error,
        });
      }

      res.json({
        success: true,
        data: {
          teamName: result.integration.teamName,
          teamId: result.integration.teamId,
          connectedAt: result.integration.connectedAt,
          isUpdate: result.isUpdate,
        },
        message: result.isUpdate
          ? "Slack integration updated successfully"
          : "Slack integration connected successfully",
      });
    } catch (error) {
      console.error("Error connecting Slack integration:", error);
      res.status(500).json({
        success: false,
        error: "Failed to connect Slack integration",
      });
    }
  });

  /**
   * POST /api/integrations/slack/test
   * Test Slack connection
   */
  router.post("/test", verifyToken, verifyAdmin(db), async (req, res) => {
    try {
      const { botToken } = req.body;

      if (!botToken || typeof botToken !== "string") {
        return res.status(400).json({
          success: false,
          error: "Bot token is required for testing",
        });
      }

      // Encrypt token for testing
      const { getEncryptionUtil } = require("../utils/encryption");
      const encryptionUtil = getEncryptionUtil();
      const encryptedToken = encryptionUtil.encrypt(botToken);

      const result = await slackService.testSlackConnection(encryptedToken);

      if (!result.success) {
        return res.status(400).json({
          success: false,
          error: result.error,
        });
      }

      res.json({
        success: true,
        data: {
          teamName: result.teamName,
          teamId: result.teamId,
          botUserId: result.botUserId,
          botName: result.botName,
        },
        message: "Slack connection test successful",
      });
    } catch (error) {
      console.error("Error testing Slack connection:", error);
      res.status(500).json({
        success: false,
        error: "Failed to test Slack connection",
      });
    }
  });

  /**
   * DELETE /api/integrations/slack/disconnect
   * Disconnect Slack integration
   */
  router.delete(
    "/disconnect",
    verifyToken,
    verifyAdmin(db),
    async (req, res) => {
      try {
        const integrationCollection = db.collection("slack_integrations");

        const result = await integrationCollection.updateMany(
          { isActive: true },
          {
            $set: {
              isActive: false,
              connectionStatus: "disconnected",
              disconnectedAt: new Date(),
              updatedAt: new Date(),
            },
          }
        );

        if (result.modifiedCount === 0) {
          return res.status(404).json({
            success: false,
            error: "No active Slack integration found",
          });
        }

        res.json({
          success: true,
          message: "Slack integration disconnected successfully",
        });
      } catch (error) {
        console.error("Error disconnecting Slack integration:", error);
        res.status(500).json({
          success: false,
          error: "Failed to disconnect Slack integration",
        });
      }
    }
  );

  /**
   * GET /api/integrations/slack/mappings
   * Get all channel mappings
   */
  router.get("/mappings", verifyToken, verifyAdmin(db), async (req, res) => {
    try {
      const mappings = await slackService.getChannelMappings();

      res.json({
        success: true,
        data: mappings.map((mapping) => ({
          _id: mapping._id,
          reportType: mapping.reportType,
          slackChannelId: mapping.slackChannelId,
          slackChannelName: mapping.slackChannelName,
          description: mapping.description,
          isActive: mapping.isActive,
          createdAt: mapping.createdAt,
          updatedAt: mapping.updatedAt,
        })),
      });
    } catch (error) {
      console.error("Error getting channel mappings:", error);
      res.status(500).json({
        success: false,
        error: "Failed to get channel mappings",
      });
    }
  });

  /**
   * POST /api/integrations/slack/mappings
   * Create or update channel mapping
   */
  router.post("/mappings", verifyToken, verifyAdmin(db), async (req, res) => {
    try {
      const { reportType, slackChannelId, description } = req.body;

      if (!reportType || !slackChannelId) {
        return res.status(400).json({
          success: false,
          error: "Report type and Slack channel ID are required",
        });
      }

      const mappingData = {
        reportType: reportType.trim(),
        slackChannelId: slackChannelId.trim(),
        description: description ? description.trim() : "",
        isActive: true,
      };

      const result = await slackService.createOrUpdateChannelMapping(
        mappingData
      );

      if (!result.success) {
        return res.status(400).json({
          success: false,
          error: result.error,
        });
      }

      res.json({
        success: true,
        data: {
          _id: result.mapping._id,
          reportType: result.mapping.reportType,
          slackChannelId: result.mapping.slackChannelId,
          slackChannelName: result.mapping.slackChannelName,
          description: result.mapping.description,
          isActive: result.mapping.isActive,
          createdAt: result.mapping.createdAt,
          updatedAt: result.mapping.updatedAt,
        },
        message: result.isUpdate
          ? "Channel mapping updated successfully"
          : "Channel mapping created successfully",
      });
    } catch (error) {
      console.error("Error creating/updating channel mapping:", error);
      res.status(500).json({
        success: false,
        error: "Failed to create/update channel mapping",
      });
    }
  });

  /**
   * DELETE /api/integrations/slack/mappings/:id
   * Delete channel mapping
   */
  router.delete(
    "/mappings/:id",
    verifyToken,
    verifyAdmin(db),
    async (req, res) => {
      try {
        const { id } = req.params;

        if (!ObjectId.isValid(id)) {
          return res.status(400).json({
            success: false,
            error: "Invalid mapping ID",
          });
        }

        const channelMappingCollection = db.collection(
          "slack_channel_mappings"
        );

        const result = await channelMappingCollection.updateOne(
          { _id: new ObjectId(id) },
          {
            $set: {
              isActive: false,
              updatedAt: new Date(),
            },
          }
        );

        if (result.matchedCount === 0) {
          return res.status(404).json({
            success: false,
            error: "Channel mapping not found",
          });
        }

        res.json({
          success: true,
          message: "Channel mapping deleted successfully",
        });
      } catch (error) {
        console.error("Error deleting channel mapping:", error);
        res.status(500).json({
          success: false,
          error: "Failed to delete channel mapping",
        });
      }
    }
  );

  /**
   * POST /api/integrations/slack/test-mapping
   * Test a channel mapping by sending a test message
   */
  router.post(
    "/test-mapping",
    verifyToken,
    verifyAdmin(db),
    async (req, res) => {
      try {
        const { channelId, reportType } = req.body;

        if (!channelId || !reportType) {
          return res.status(400).json({
            success: false,
            error: "Channel ID and report type are required",
          });
        }

        const result = await slackService.testChannelMapping(
          channelId,
          reportType
        );

        if (!result.success) {
          return res.status(400).json({
            success: false,
            error: result.error,
          });
        }

        res.json({
          success: true,
          message: "Test message sent successfully",
          data: {
            timestamp: result.timestamp,
          },
        });
      } catch (error) {
        console.error("Error testing channel mapping:", error);
        res.status(500).json({
          success: false,
          error: "Failed to test channel mapping",
        });
      }
    }
  );

  return router;
}

module.exports = createSlackIntegrationRoutes;
