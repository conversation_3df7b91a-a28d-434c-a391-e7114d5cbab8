# JWT Configuration
JWT_SECRET = your-jwt-secret-key-here

# Slack Integration Configuration
# Generate a 32-character encryption key for storing Slack bot tokens securely
SLACK_ENCRYPTION_KEY = your-32-character-encryption-key-here

# Slack API Rate Limiting
SLACK_RATE_LIMIT_REQUESTS = 50
SLACK_RATE_LIMIT_WINDOW_MS = 60000

# Optional: Default Slack settings
SLACK_DEFAULT_CHANNEL = general
SLACK_APP_NAME = WPDev Daily Desk

# MongoDB Configuration (if needed)
# MONGODB_URI = mongodb://127.0.0.1:27017/wpdevDB
