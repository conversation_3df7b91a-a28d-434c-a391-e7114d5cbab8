const { ObjectId } = require("mongodb");

/**
 * ThreadTracker Schema
 * Tracks Slack thread timestamps for monthly report threads
 */
class ThreadTracker {
  constructor(data) {
    this._id = data._id || new ObjectId();
    this.userId = data.userId; // ObjectId reference to user
    this.reportType = data.reportType; // Must match ChannelMapping.reportType
    this.slackChannelId = data.slackChannelId; // Slack channel ID
    this.monthKey = data.monthKey; // Format: "YYYY-MM" (e.g., "2025-08")
    this.threadTs = data.threadTs; // Slack thread timestamp
    this.threadUrl = data.threadUrl || null; // Generated Slack thread URL
    this.messageCount = data.messageCount || 0; // Number of messages in thread
    this.lastMessageAt = data.lastMessageAt || null; // Last message timestamp
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
    this.isActive = data.isActive !== undefined ? data.isActive : true;
  }

  /**
   * Validate the ThreadTracker data
   * @returns {Object} { isValid: boolean, errors: string[] }
   */
  validate() {
    const errors = [];

    if (!this.userId) {
      errors.push('User ID is required');
    }

    if (!this.reportType || typeof this.reportType !== 'string') {
      errors.push('Report type is required and must be a string');
    }

    if (!this.slackChannelId || typeof this.slackChannelId !== 'string') {
      errors.push('Slack channel ID is required and must be a string');
    }

    if (!this.monthKey || typeof this.monthKey !== 'string') {
      errors.push('Month key is required and must be a string');
    }

    if (!this.threadTs || typeof this.threadTs !== 'string') {
      errors.push('Thread timestamp is required and must be a string');
    }

    // Validate month key format (YYYY-MM)
    if (this.monthKey && !/^\d{4}-\d{2}$/.test(this.monthKey)) {
      errors.push('Month key must be in format YYYY-MM');
    }

    // Validate Slack channel ID format
    if (this.slackChannelId && !/^C[A-Z0-9]{8,}$/.test(this.slackChannelId)) {
      errors.push('Slack channel ID must be in format C followed by alphanumeric characters');
    }

    // Validate thread timestamp format (Slack timestamps are decimal strings)
    if (this.threadTs && !/^\d+\.\d+$/.test(this.threadTs)) {
      errors.push('Thread timestamp must be in Slack timestamp format (e.g., "1234567890.123456")');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate Slack thread URL
   * @param {string} teamId - Slack team ID
   * @returns {string} Slack thread URL
   */
  generateThreadUrl(teamId) {
    if (!teamId || !this.slackChannelId || !this.threadTs) {
      return null;
    }
    
    // Format: https://app.slack.com/client/TEAM_ID/CHANNEL_ID/thread/THREAD_TS
    const threadTsForUrl = this.threadTs.replace('.', '');
    this.threadUrl = `https://app.slack.com/client/${teamId}/${this.slackChannelId}/thread/${threadTsForUrl}`;
    return this.threadUrl;
  }

  /**
   * Increment message count
   */
  incrementMessageCount() {
    this.messageCount += 1;
    this.lastMessageAt = new Date();
    this.updatedAt = new Date();
  }

  /**
   * Convert to plain object for database storage
   * @returns {Object}
   */
  toObject() {
    return {
      _id: this._id,
      userId: this.userId,
      reportType: this.reportType,
      slackChannelId: this.slackChannelId,
      monthKey: this.monthKey,
      threadTs: this.threadTs,
      threadUrl: this.threadUrl,
      messageCount: this.messageCount,
      lastMessageAt: this.lastMessageAt,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      isActive: this.isActive
    };
  }

  /**
   * Create from database document
   * @param {Object} doc - Database document
   * @returns {ThreadTracker}
   */
  static fromDocument(doc) {
    return new ThreadTracker(doc);
  }

  /**
   * Get collection indexes
   * @returns {Array} Array of index specifications
   */
  static getIndexes() {
    return [
      { 
        key: { userId: 1, reportType: 1, monthKey: 1, slackChannelId: 1 }, 
        unique: true 
      }, // Prevent duplicate threads
      { key: { userId: 1 } },
      { key: { reportType: 1 } },
      { key: { slackChannelId: 1 } },
      { key: { monthKey: 1 } },
      { key: { threadTs: 1 } },
      { key: { isActive: 1 } },
      { key: { updatedAt: -1 } }
    ];
  }

  /**
   * Generate month key from date
   * @param {Date} date - Date to generate month key from
   * @returns {string} Month key in format YYYY-MM
   */
  static generateMonthKey(date = new Date()) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  }
}

module.exports = ThreadTracker;
