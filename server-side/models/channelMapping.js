const { ObjectId } = require("mongodb");

/**
 * ChannelMapping Schema
 * Maps report types to Slack channels
 */
class ChannelMapping {
  constructor(data) {
    this._id = data._id || new ObjectId();
    this.reportType = data.reportType; // e.g., "daily_work", "shopify"
    this.slackChannelId = data.slackChannelId; // Slack channel ID (*********)
    this.slackChannelName = data.slackChannelName; // Human-readable channel name
    this.integrationId = data.integrationId; // Reference to SlackIntegration
    this.label = data.label || data.reportType; // Optional human-friendly label
    this.isActive = data.isActive !== undefined ? data.isActive : true;
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
    this.createdByAdminId = data.createdByAdminId; // ObjectId reference to admin user
    this.lastTestAt = data.lastTestAt || null; // Last successful test message
    this.testStatus = data.testStatus || 'pending'; // 'success', 'failed', 'pending'
  }

  /**
   * Validate the ChannelMapping data
   * @returns {Object} { isValid: boolean, errors: string[] }
   */
  validate() {
    const errors = [];

    if (!this.reportType || typeof this.reportType !== 'string') {
      errors.push('Report type is required and must be a string');
    }

    if (!this.slackChannelId || typeof this.slackChannelId !== 'string') {
      errors.push('Slack channel ID is required and must be a string');
    }

    if (!this.integrationId) {
      errors.push('Integration ID is required');
    }

    if (!this.createdByAdminId) {
      errors.push('Created by admin ID is required');
    }

    // Validate report type format (alphanumeric and underscores only)
    if (this.reportType && !/^[a-zA-Z0-9_]+$/.test(this.reportType)) {
      errors.push('Report type must contain only alphanumeric characters and underscores');
    }

    // Validate Slack channel ID format
    if (this.slackChannelId && !/^C[A-Z0-9]{8,}$/.test(this.slackChannelId)) {
      errors.push('Slack channel ID must be in format C followed by alphanumeric characters');
    }

    if (!['success', 'failed', 'pending'].includes(this.testStatus)) {
      errors.push('Test status must be one of: success, failed, pending');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Convert to plain object for database storage
   * @returns {Object}
   */
  toObject() {
    return {
      _id: this._id,
      reportType: this.reportType,
      slackChannelId: this.slackChannelId,
      slackChannelName: this.slackChannelName,
      integrationId: this.integrationId,
      label: this.label,
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      createdByAdminId: this.createdByAdminId,
      lastTestAt: this.lastTestAt,
      testStatus: this.testStatus
    };
  }

  /**
   * Create from database document
   * @param {Object} doc - Database document
   * @returns {ChannelMapping}
   */
  static fromDocument(doc) {
    return new ChannelMapping(doc);
  }

  /**
   * Get collection indexes
   * @returns {Array} Array of index specifications
   */
  static getIndexes() {
    return [
      { key: { reportType: 1, integrationId: 1 }, unique: true }, // One mapping per report type per integration
      { key: { integrationId: 1 } },
      { key: { slackChannelId: 1 } },
      { key: { isActive: 1 } },
      { key: { createdByAdminId: 1 } },
      { key: { updatedAt: -1 } }
    ];
  }
}

module.exports = ChannelMapping;
