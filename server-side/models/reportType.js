/**
 * Report Type Model
 * Represents different types of reports that can be submitted
 */
class ReportType {
  constructor(data = {}) {
    this.key = data.key || null; // Unique identifier (e.g., "daily_work")
    this.name = data.name || null; // Display name (e.g., "Daily Work Report")
    this.description = data.description || null; // Description of the report type
    this.isActive = data.isActive !== undefined ? data.isActive : true; // Whether this type is available for selection
    this.sortOrder = data.sortOrder || 0; // Order for display in UI
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
  }

  /**
   * Convert to plain object for database storage
   * @returns {Object} Plain object representation
   */
  toObject() {
    return {
      key: this.key,
      name: this.name,
      description: this.description,
      isActive: this.isActive,
      sortOrder: this.sortOrder,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * Create ReportType instance from database document
   * @param {Object} doc - Database document
   * @returns {ReportType} ReportType instance
   */
  static fromDocument(doc) {
    return new ReportType({
      ...doc,
      _id: doc._id
    });
  }

  /**
   * Validate report type data
   * @returns {Object} Validation result
   */
  validate() {
    const errors = [];

    if (!this.key || typeof this.key !== 'string' || this.key.trim().length === 0) {
      errors.push('Key is required and must be a non-empty string');
    }

    if (!this.name || typeof this.name !== 'string' || this.name.trim().length === 0) {
      errors.push('Name is required and must be a non-empty string');
    }

    if (this.key && !/^[a-z0-9_]+$/.test(this.key)) {
      errors.push('Key must contain only lowercase letters, numbers, and underscores');
    }

    if (typeof this.isActive !== 'boolean') {
      errors.push('isActive must be a boolean');
    }

    if (typeof this.sortOrder !== 'number') {
      errors.push('sortOrder must be a number');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get default report types
   * @returns {Array<ReportType>} Array of default report types
   */
  static getDefaults() {
    return [
      new ReportType({
        key: 'daily_work',
        name: 'Daily Work Report',
        description: 'Standard daily work update including tickets, reviews, and tasks',
        isActive: true,
        sortOrder: 1
      }),
      new ReportType({
        key: 'shopify',
        name: 'Shopify Report',
        description: 'Shopify-related work and updates',
        isActive: true,
        sortOrder: 2
      }),
      new ReportType({
        key: 'support',
        name: 'Support Report',
        description: 'Customer support activities and resolutions',
        isActive: true,
        sortOrder: 3
      }),
      new ReportType({
        key: 'project_update',
        name: 'Project Update',
        description: 'Project milestone and progress updates',
        isActive: true,
        sortOrder: 4
      }),
      new ReportType({
        key: 'weekly_summary',
        name: 'Weekly Summary',
        description: 'Weekly summary of accomplishments and goals',
        isActive: true,
        sortOrder: 5
      })
    ];
  }
}

module.exports = ReportType;
