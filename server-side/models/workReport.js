const { ObjectId } = require("mongodb");

/**
 * WorkReport Schema
 * Extended version of the existing reports collection with Slack integration support
 */
class WorkReport {
  constructor(data) {
    // Existing fields from current reports collection
    this._id = data._id || new ObjectId();
    this.email = data.email;
    this.name = data.name;
    this.report_date = data.report_date;
    
    // All existing report fields
    this.wpdev_ticket_reply = data.wpdev_ticket_reply || 0;
    this.storeware_ticket_reply = data.storeware_ticket_reply || 0;
    this.xcloud_ticket_reply = data.xcloud_ticket_reply || 0;
    this.easyjobs_ticket_reply = data.easyjobs_ticket_reply || 0;
    this.userback_reply = data.userback_reply || 0;
    this.wpdev_crisp_reply = data.wpdev_crisp_reply || 0;
    this.wpdev_crisp_magic_browser_reply = data.wpdev_crisp_magic_browser_reply || 0;
    this.storeware_crisp_reply = data.storeware_crisp_reply || 0;
    this.storeware_crisp_magic_browser_reply = data.storeware_crisp_magic_browser_reply || 0;
    this.xcloud_crisp_reply = data.xcloud_crisp_reply || 0;
    this.xcloud_crisp_magic_browser_reply = data.xcloud_crisp_magic_browser_reply || 0;
    this.wp_org_reply = data.wp_org_reply || 0;
    this.fb_post_reply = data.fb_post_reply || 0;
    this.hs_ticket_close = data.hs_ticket_close || 0;
    this.shopify_app_review = data.shopify_app_review || 0;
    this.shopify_app_review_get = data.shopify_app_review_get || 0;
    this.wporg_review_get = data.wporg_review_get || 0;
    this.trustpilot_review_links = data.trustpilot_review_links || '';
    this.hs_ratings = data.hs_ratings || 0;
    this.crisp_ratings = data.crisp_ratings || 0;
    this.additional_notes = data.additional_notes || '';

    // New Slack integration fields
    this.reportType = data.reportType || 'daily_work'; // Default report type
    this.slackMetadata = data.slackMetadata || {
      posted: false,
      threadTs: null,
      threadUrl: null,
      channelId: null,
      postedAt: null,
      errorMessage: null
    };
    this.submittedAt = data.submittedAt || new Date();
    this.userId = data.userId || null; // ObjectId reference to user (if available)
  }

  /**
   * Validate the WorkReport data
   * @returns {Object} { isValid: boolean, errors: string[] }
   */
  validate() {
    const errors = [];

    if (!this.email || typeof this.email !== 'string') {
      errors.push('Email is required and must be a string');
    }

    if (!this.name || typeof this.name !== 'string') {
      errors.push('Name is required and must be a string');
    }

    if (!this.report_date || typeof this.report_date !== 'string') {
      errors.push('Report date is required and must be a string');
    }

    if (!this.reportType || typeof this.reportType !== 'string') {
      errors.push('Report type is required and must be a string');
    }

    // Validate report type format
    if (this.reportType && !/^[a-zA-Z0-9_]+$/.test(this.reportType)) {
      errors.push('Report type must contain only alphanumeric characters and underscores');
    }

    // Validate email format
    if (this.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.email)) {
      errors.push('Email must be in valid format');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Update Slack metadata
   * @param {Object} metadata - Slack metadata to update
   */
  updateSlackMetadata(metadata) {
    this.slackMetadata = {
      ...this.slackMetadata,
      ...metadata
    };
  }

  /**
   * Mark as posted to Slack
   * @param {string} threadTs - Slack thread timestamp
   * @param {string} threadUrl - Slack thread URL
   * @param {string} channelId - Slack channel ID
   */
  markAsPostedToSlack(threadTs, threadUrl, channelId) {
    this.slackMetadata = {
      ...this.slackMetadata,
      posted: true,
      threadTs,
      threadUrl,
      channelId,
      postedAt: new Date(),
      errorMessage: null
    };
  }

  /**
   * Mark Slack posting as failed
   * @param {string} errorMessage - Error message
   */
  markSlackPostingFailed(errorMessage) {
    this.slackMetadata = {
      ...this.slackMetadata,
      posted: false,
      errorMessage,
      postedAt: null
    };
  }

  /**
   * Convert to plain object for database storage
   * @returns {Object}
   */
  toObject() {
    return {
      _id: this._id,
      email: this.email,
      name: this.name,
      report_date: this.report_date,
      wpdev_ticket_reply: this.wpdev_ticket_reply,
      storeware_ticket_reply: this.storeware_ticket_reply,
      xcloud_ticket_reply: this.xcloud_ticket_reply,
      easyjobs_ticket_reply: this.easyjobs_ticket_reply,
      userback_reply: this.userback_reply,
      wpdev_crisp_reply: this.wpdev_crisp_reply,
      wpdev_crisp_magic_browser_reply: this.wpdev_crisp_magic_browser_reply,
      storeware_crisp_reply: this.storeware_crisp_reply,
      storeware_crisp_magic_browser_reply: this.storeware_crisp_magic_browser_reply,
      xcloud_crisp_reply: this.xcloud_crisp_reply,
      xcloud_crisp_magic_browser_reply: this.xcloud_crisp_magic_browser_reply,
      wp_org_reply: this.wp_org_reply,
      fb_post_reply: this.fb_post_reply,
      hs_ticket_close: this.hs_ticket_close,
      shopify_app_review: this.shopify_app_review,
      shopify_app_review_get: this.shopify_app_review_get,
      wporg_review_get: this.wporg_review_get,
      trustpilot_review_links: this.trustpilot_review_links,
      hs_ratings: this.hs_ratings,
      crisp_ratings: this.crisp_ratings,
      additional_notes: this.additional_notes,
      reportType: this.reportType,
      slackMetadata: this.slackMetadata,
      submittedAt: this.submittedAt,
      userId: this.userId
    };
  }

  /**
   * Create from database document
   * @param {Object} doc - Database document
   * @returns {WorkReport}
   */
  static fromDocument(doc) {
    return new WorkReport(doc);
  }

  /**
   * Get collection indexes
   * @returns {Array} Array of index specifications
   */
  static getIndexes() {
    return [
      { key: { email: 1, report_date: 1 }, unique: true }, // Existing unique constraint
      { key: { email: 1 } },
      { key: { reportType: 1 } },
      { key: { submittedAt: -1 } },
      { key: { userId: 1 } },
      { key: { 'slackMetadata.posted': 1 } },
      { key: { 'slackMetadata.channelId': 1 } }
    ];
  }

  /**
   * Get available report types
   * @returns {Array} Array of available report types
   */
  static getAvailableReportTypes() {
    return [
      { value: 'daily_work', label: 'Daily Work Report' },
      { value: 'shopify', label: 'Shopify Report' },
      { value: 'support', label: 'Support Report' },
      { value: 'development', label: 'Development Report' },
      { value: 'marketing', label: 'Marketing Report' }
    ];
  }
}

module.exports = WorkReport;
