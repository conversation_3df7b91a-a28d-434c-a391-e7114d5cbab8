const { ObjectId } = require("mongodb");

/**
 * SlackIntegration Schema
 * Represents a Slack workspace integration configuration
 */
class SlackIntegration {
  constructor(data) {
    this._id = data._id || new ObjectId();
    this.botToken = data.botToken; // Encrypted bot token
    this.teamId = data.teamId; // Slack team/workspace ID
    this.teamName = data.teamName; // Human-readable team name
    this.connectedAt = data.connectedAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
    this.createdByAdminId = data.createdByAdminId; // ObjectId reference to admin user
    this.isActive = data.isActive !== undefined ? data.isActive : true;
    this.lastTestAt = data.lastTestAt || null; // Last successful connection test
    this.connectionStatus = data.connectionStatus || 'pending'; // 'connected', 'failed', 'pending'
    this.errorMessage = data.errorMessage || null; // Last error message if any
  }

  /**
   * Validate the SlackIntegration data
   * @returns {Object} { isValid: boolean, errors: string[] }
   */
  validate() {
    const errors = [];

    if (!this.botToken || typeof this.botToken !== 'string') {
      errors.push('Bot token is required and must be a string');
    }

    if (!this.teamId || typeof this.teamId !== 'string') {
      errors.push('Team ID is required and must be a string');
    }

    if (!this.createdByAdminId) {
      errors.push('Created by admin ID is required');
    }

    if (!['connected', 'failed', 'pending'].includes(this.connectionStatus)) {
      errors.push('Connection status must be one of: connected, failed, pending');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Convert to plain object for database storage
   * @returns {Object}
   */
  toObject() {
    return {
      _id: this._id,
      botToken: this.botToken,
      teamId: this.teamId,
      teamName: this.teamName,
      connectedAt: this.connectedAt,
      updatedAt: this.updatedAt,
      createdByAdminId: this.createdByAdminId,
      isActive: this.isActive,
      lastTestAt: this.lastTestAt,
      connectionStatus: this.connectionStatus,
      errorMessage: this.errorMessage
    };
  }

  /**
   * Create from database document
   * @param {Object} doc - Database document
   * @returns {SlackIntegration}
   */
  static fromDocument(doc) {
    return new SlackIntegration(doc);
  }

  /**
   * Get collection indexes
   * @returns {Array} Array of index specifications
   */
  static getIndexes() {
    return [
      { key: { teamId: 1 }, unique: true }, // One integration per team
      { key: { createdByAdminId: 1 } },
      { key: { isActive: 1 } },
      { key: { connectionStatus: 1 } },
      { key: { updatedAt: -1 } }
    ];
  }
}

module.exports = SlackIntegration;
