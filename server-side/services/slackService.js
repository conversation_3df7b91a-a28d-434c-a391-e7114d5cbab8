const { WebClient } = require("@slack/web-api");
const { getEncryptionUtil } = require("../utils/encryption");
const ThreadTracker = require("../models/threadTracker");

/**
 * Slack Service
 * Handles all Slack API interactions
 */
class SlackService {
  constructor(botToken) {
    if (!botToken) {
      throw new Error("Bot token is required");
    }

    this.client = new WebClient(botToken);
    this.rateLimitDelay = 1000; // 1 second base delay
    this.maxRetries = 3;
  }

  /**
   * Test Slack connection
   * @returns {Promise<Object>} Connection test result
   */
  async testConnection() {
    try {
      const response = await this.client.auth.test();

      return {
        success: true,
        teamId: response.team_id,
        teamName: response.team,
        botUserId: response.user_id,
        botName: response.user,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        code: error.code,
      };
    }
  }

  /**
   * Get channel information
   * @param {string} channelId - Slack channel ID
   * @returns {Promise<Object>} Channel information
   */
  async getChannelInfo(channelId) {
    try {
      const response = await this.client.conversations.info({
        channel: channelId,
      });

      return {
        success: true,
        channel: {
          id: response.channel.id,
          name: response.channel.name,
          isPrivate: response.channel.is_private,
          isMember: response.channel.is_member,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        code: error.code,
      };
    }
  }

  /**
   * Post a message to a channel
   * @param {string} channelId - Slack channel ID
   * @param {Object} messageOptions - Message options
   * @returns {Promise<Object>} Post result
   */
  async postMessage(channelId, messageOptions) {
    try {
      const response = await this.client.chat.postMessage({
        channel: channelId,
        ...messageOptions,
      });

      return {
        success: true,
        timestamp: response.ts,
        message: response.message,
      };
    } catch (error) {
      if (error.code === "rate_limited") {
        // Handle rate limiting
        const retryAfter =
          error.headers["retry-after"] || this.rateLimitDelay / 1000;
        throw new Error(`Rate limited. Retry after ${retryAfter} seconds`);
      }

      return {
        success: false,
        error: error.message,
        code: error.code,
      };
    }
  }

  /**
   * Post a message in a thread
   * @param {string} channelId - Slack channel ID
   * @param {string} threadTs - Thread timestamp
   * @param {Object} messageOptions - Message options
   * @returns {Promise<Object>} Post result
   */
  async postThreadMessage(channelId, threadTs, messageOptions) {
    return this.postMessage(channelId, {
      ...messageOptions,
      thread_ts: threadTs,
    });
  }

  /**
   * Send a test message to a channel
   * @param {string} channelId - Slack channel ID
   * @param {string} reportType - Report type being tested
   * @returns {Promise<Object>} Test result
   */
  async sendTestMessage(channelId, reportType) {
    const testMessage = {
      text: `🧪 Test message for ${reportType} reports`,
      blocks: [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `🧪 *Test Message*\n\nThis is a test message for *${reportType}* reports.\n\nIf you can see this message, the Slack integration is working correctly!`,
          },
        },
        {
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `Test sent at ${new Date().toISOString()}`,
            },
          ],
        },
      ],
    };

    return this.postMessage(channelId, testMessage);
  }

  /**
   * Create a formatted report message using Slack Block Kit
   * @param {Object} report - Report data
   * @param {Object} user - User data
   * @param {boolean} isThreadRoot - Whether this is the root message of a thread
   * @returns {Object} Formatted message
   */
  createReportMessage(report, user, isThreadRoot = false) {
    const reportDate = new Date(report.submittedAt || new Date());
    const formattedDate = reportDate.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });

    if (isThreadRoot) {
      return this.createThreadRootMessage(report, user, formattedDate);
    } else {
      return this.createThreadReplyMessage(report, user, formattedDate);
    }
  }

  /**
   * Create thread root message
   * @param {Object} report - Report data
   * @param {Object} user - User data
   * @param {string} formattedDate - Formatted date
   * @returns {Object} Root message
   */
  createThreadRootMessage(report, user, formattedDate) {
    const monthYear = new Date(
      report.submittedAt || new Date()
    ).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
    });

    const reportTypeEmoji = this.getReportTypeEmoji(report.reportType);

    return {
      text: `📁 Monthly Report Thread for ${user.displayName || user.name}`,
      blocks: [
        {
          type: "header",
          text: {
            type: "plain_text",
            text: `📁 Monthly Report Thread`,
          },
        },
        {
          type: "section",
          fields: [
            {
              type: "mrkdwn",
              text: `*User:*\n${user.displayName || user.name}`,
            },
            {
              type: "mrkdwn",
              text: `*Report Type:*\n${reportTypeEmoji} ${report.reportType}`,
            },
            {
              type: "mrkdwn",
              text: `*Month:*\n${monthYear}`,
            },
            {
              type: "mrkdwn",
              text: `*Started:*\n${formattedDate}`,
            },
          ],
        },
        {
          type: "divider",
        },
        {
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `This thread will collect all ${
                report.reportType
              } reports submitted by ${
                user.displayName || user.name
              } for ${monthYear}.`,
            },
          ],
        },
      ],
    };
  }

  /**
   * Create thread reply message
   * @param {Object} report - Report data
   * @param {Object} user - User data
   * @param {string} formattedDate - Formatted date
   * @returns {Object} Reply message
   */
  createThreadReplyMessage(report, user, formattedDate) {
    const reportTypeEmoji = this.getReportTypeEmoji(report.reportType);

    // Create summary based on report type
    const summary = this.createReportSummary(report);

    return {
      text: `Report submitted by ${user.displayName || user.name}`,
      blocks: [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `✅ *Report submitted by:* ${
              user.displayName || user.name
            }\n*Date:* ${formattedDate}\n*Type:* ${reportTypeEmoji} ${
              report.reportType
            }`,
          },
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: summary,
          },
        },
        ...(report.additional_notes
          ? [
              {
                type: "section",
                text: {
                  type: "mrkdwn",
                  text: `*Additional Notes:*\n${report.additional_notes}`,
                },
              },
            ]
          : []),
        {
          type: "divider",
        },
      ],
    };
  }

  /**
   * Get emoji for report type
   * @param {string} reportType - Report type
   * @returns {string} Emoji
   */
  getReportTypeEmoji(reportType) {
    const emojiMap = {
      daily_work: "📝",
      shopify: "🛒",
      support: "🎧",
      development: "💻",
      marketing: "📢",
    };

    return emojiMap[reportType] || "📄";
  }

  /**
   * Create report summary based on report data
   * @param {Object} report - Report data
   * @returns {string} Summary text
   */
  createReportSummary(report) {
    const summaryParts = [];

    // Ticket replies
    const ticketReplies = [
      { label: "WPDev Tickets", value: report.wpdev_ticket_reply },
      { label: "Storeware Tickets", value: report.storeware_ticket_reply },
      { label: "XCloud Tickets", value: report.xcloud_ticket_reply },
      { label: "EasyJobs Tickets", value: report.easyjobs_ticket_reply },
    ].filter((item) => item.value > 0);

    if (ticketReplies.length > 0) {
      summaryParts.push("*Ticket Replies:*");
      ticketReplies.forEach((item) => {
        summaryParts.push(`• ${item.label}: ${item.value}`);
      });
    }

    // Crisp replies
    const crispReplies = [
      { label: "WPDev Crisp", value: report.wpdev_crisp_reply },
      { label: "Storeware Crisp", value: report.storeware_crisp_reply },
      { label: "XCloud Crisp", value: report.xcloud_crisp_reply },
    ].filter((item) => item.value > 0);

    if (crispReplies.length > 0) {
      if (summaryParts.length > 0) summaryParts.push("");
      summaryParts.push("*Crisp Replies:*");
      crispReplies.forEach((item) => {
        summaryParts.push(`• ${item.label}: ${item.value}`);
      });
    }

    // Reviews and ratings
    const reviews = [
      { label: "Shopify Reviews Requested", value: report.shopify_app_review },
      {
        label: "Shopify Reviews Received",
        value: report.shopify_app_review_get,
      },
      { label: "WP.org Reviews", value: report.wporg_review_get },
    ].filter((item) => item.value > 0);

    if (reviews.length > 0) {
      if (summaryParts.length > 0) summaryParts.push("");
      summaryParts.push("*Reviews & Ratings:*");
      reviews.forEach((item) => {
        summaryParts.push(`• ${item.label}: ${item.value}`);
      });
    }

    return summaryParts.length > 0
      ? summaryParts.join("\n")
      : "No specific metrics reported.";
  }

  /**
   * Create SlackService instance from encrypted bot token
   * @param {string} encryptedToken - Encrypted bot token
   * @returns {SlackService} SlackService instance
   */
  static fromEncryptedToken(encryptedToken) {
    const encryptionUtil = getEncryptionUtil();
    const decryptedToken = encryptionUtil.decrypt(encryptedToken);
    return new SlackService(decryptedToken);
  }
}

module.exports = SlackService;
