const { ObjectId } = require('mongodb');
const SlackService = require('./slackService');
const ThreadManager = require('./threadManager');
const SlackIntegration = require('../models/slackIntegration');
const ChannelMapping = require('../models/channelMapping');
const WorkReport = require('../models/workReport');
const { getEncryptionUtil } = require('../utils/encryption');
const { SlackRetryHandler, RateLimiter } = require('../utils/retryHandler');

/**
 * Slack Integration Service
 * Main orchestrator for all Slack integration functionality
 */
class SlackIntegrationService {
  constructor(db) {
    this.db = db;
    this.threadManager = new ThreadManager(db);
    this.retryHandler = new SlackRetryHandler();
    this.rateLimiter = new RateLimiter({
      requestsPerWindow: parseInt(process.env.SLACK_RATE_LIMIT_REQUESTS) || 50,
      windowMs: parseInt(process.env.SLACK_RATE_LIMIT_WINDOW_MS) || 60000
    });
    
    // Collections
    this.integrationCollection = db.collection('slack_integrations');
    this.channelMappingCollection = db.collection('slack_channel_mappings');
    this.userCollection = db.collection('users');
    this.reportCollection = db.collection('reports');
  }

  /**
   * Process a report submission and post to Slack
   * @param {Object} report - Report data
   * @param {ObjectId} userId - User ID
   * @returns {Promise<Object>} Processing result
   */
  async processReportSubmission(report, userId) {
    try {
      // Get channel mapping for report type
      const channelMapping = await this.getChannelMapping(report.reportType);
      if (!channelMapping) {
        return {
          success: false,
          error: `No Slack channel mapping found for report type: ${report.reportType}`,
          skipSlack: true
        };
      }

      // Get active integration
      const integration = await this.getActiveIntegration();
      if (!integration) {
        return {
          success: false,
          error: 'No active Slack integration found',
          skipSlack: true
        };
      }

      // Get user data
      const user = await this.getUserData(userId);
      if (!user) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      // Create Slack service
      const slackService = SlackService.fromEncryptedToken(integration.botToken);

      // Rate limiting
      await this.rateLimiter.waitForAllowance();

      // Find or create thread
      const threadResult = await this.threadManager.findOrCreateThread({
        userId,
        reportType: report.reportType,
        slackChannelId: channelMapping.slackChannelId,
        submissionDate: report.submittedAt || new Date(),
        slackService,
        user,
        report
      });

      if (!threadResult.success) {
        return {
          success: false,
          error: threadResult.error
        };
      }

      let messageResult;

      if (threadResult.isNewThread) {
        // Thread was just created, no need to post again
        messageResult = { success: true };
      } else {
        // Post to existing thread
        messageResult = await this.threadManager.postToThread(
          threadResult.threadTs,
          channelMapping.slackChannelId,
          slackService,
          user,
          report
        );
      }

      if (!messageResult.success) {
        return {
          success: false,
          error: messageResult.error
        };
      }

      return {
        success: true,
        threadTs: threadResult.threadTs,
        threadUrl: threadResult.threadUrl,
        channelId: channelMapping.slackChannelId,
        isNewThread: threadResult.isNewThread
      };

    } catch (error) {
      console.error('Error processing report submission:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get channel mapping for report type
   * @param {string} reportType - Report type
   * @returns {Promise<ChannelMapping|null>} Channel mapping
   */
  async getChannelMapping(reportType) {
    const doc = await this.channelMappingCollection.findOne({
      reportType,
      isActive: true
    });
    
    return doc ? ChannelMapping.fromDocument(doc) : null;
  }

  /**
   * Get active Slack integration
   * @returns {Promise<SlackIntegration|null>} Active integration
   */
  async getActiveIntegration() {
    const doc = await this.integrationCollection.findOne({
      isActive: true,
      connectionStatus: 'connected'
    });
    
    return doc ? SlackIntegration.fromDocument(doc) : null;
  }

  /**
   * Get user data with display name resolution
   * @param {ObjectId} userId - User ID
   * @returns {Promise<Object|null>} User data
   */
  async getUserData(userId) {
    // Try to find by ObjectId first
    let user = await this.userCollection.findOne({ _id: new ObjectId(userId) });
    
    if (!user) {
      // If not found by ObjectId, try to find by email (for backward compatibility)
      user = await this.userCollection.findOne({ email: userId });
    }

    if (!user) {
      return null;
    }

    // Resolve display name
    const displayName = this.resolveDisplayName(user);

    return {
      ...user,
      displayName
    };
  }

  /**
   * Resolve user display name
   * @param {Object} user - User object
   * @returns {string} Display name
   */
  resolveDisplayName(user) {
    // Priority: displayName > name > email (before @)
    if (user.displayName) {
      return user.displayName;
    }
    
    if (user.name) {
      return user.name;
    }
    
    if (user.email) {
      return user.email.split('@')[0];
    }
    
    return 'Unknown User';
  }

  /**
   * Test Slack connection
   * @param {string} encryptedBotToken - Encrypted bot token
   * @returns {Promise<Object>} Test result
   */
  async testSlackConnection(encryptedBotToken) {
    try {
      const slackService = SlackService.fromEncryptedToken(encryptedBotToken);
      
      return await this.retryHandler.executeSlackCall(async () => {
        return await slackService.testConnection();
      });
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Test channel mapping
   * @param {string} channelId - Slack channel ID
   * @param {string} reportType - Report type
   * @returns {Promise<Object>} Test result
   */
  async testChannelMapping(channelId, reportType) {
    try {
      const integration = await this.getActiveIntegration();
      if (!integration) {
        return {
          success: false,
          error: 'No active Slack integration found'
        };
      }

      const slackService = SlackService.fromEncryptedToken(integration.botToken);
      
      // Rate limiting
      await this.rateLimiter.waitForAllowance();

      return await this.retryHandler.executeSlackCall(async () => {
        return await slackService.sendTestMessage(channelId, reportType);
      });
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create or update Slack integration
   * @param {string} botToken - Bot token (plain text)
   * @param {ObjectId} adminId - Admin user ID
   * @returns {Promise<Object>} Creation result
   */
  async createOrUpdateIntegration(botToken, adminId) {
    try {
      // Encrypt bot token
      const encryptionUtil = getEncryptionUtil();
      const encryptedToken = encryptionUtil.encrypt(botToken);

      // Test connection
      const testResult = await this.testSlackConnection(encryptedToken);
      if (!testResult.success) {
        return {
          success: false,
          error: `Connection test failed: ${testResult.error}`
        };
      }

      // Create or update integration
      const integrationData = {
        botToken: encryptedToken,
        teamId: testResult.teamId,
        teamName: testResult.teamName,
        connectionStatus: 'connected',
        lastTestAt: new Date(),
        updatedAt: new Date(),
        createdByAdminId: new ObjectId(adminId),
        isActive: true,
        errorMessage: null
      };

      // Check if integration already exists for this team
      const existingIntegration = await this.integrationCollection.findOne({
        teamId: testResult.teamId
      });

      let result;
      if (existingIntegration) {
        // Update existing
        result = await this.integrationCollection.updateOne(
          { _id: existingIntegration._id },
          { $set: integrationData }
        );
        integrationData._id = existingIntegration._id;
      } else {
        // Create new
        integrationData.connectedAt = new Date();
        result = await this.integrationCollection.insertOne(integrationData);
        integrationData._id = result.insertedId;
      }

      return {
        success: true,
        integration: SlackIntegration.fromDocument(integrationData),
        isUpdate: !!existingIntegration
      };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create or update channel mapping
   * @param {Object} mappingData - Mapping data
   * @returns {Promise<Object>} Creation result
   */
  async createOrUpdateChannelMapping(mappingData) {
    try {
      const integration = await this.getActiveIntegration();
      if (!integration) {
        return {
          success: false,
          error: 'No active Slack integration found'
        };
      }

      // Validate channel
      const slackService = SlackService.fromEncryptedToken(integration.botToken);
      const channelInfo = await slackService.getChannelInfo(mappingData.slackChannelId);
      
      if (!channelInfo.success) {
        return {
          success: false,
          error: `Invalid channel: ${channelInfo.error}`
        };
      }

      const channelMapping = new ChannelMapping({
        ...mappingData,
        integrationId: integration._id,
        slackChannelName: channelInfo.channel.name,
        updatedAt: new Date()
      });

      const validation = channelMapping.validate();
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.errors.join(', ')
        };
      }

      // Check if mapping already exists
      const existingMapping = await this.channelMappingCollection.findOne({
        reportType: mappingData.reportType,
        integrationId: integration._id
      });

      let result;
      if (existingMapping) {
        // Update existing
        result = await this.channelMappingCollection.updateOne(
          { _id: existingMapping._id },
          { $set: channelMapping.toObject() }
        );
        channelMapping._id = existingMapping._id;
      } else {
        // Create new
        channelMapping.createdAt = new Date();
        result = await this.channelMappingCollection.insertOne(channelMapping.toObject());
        channelMapping._id = result.insertedId;
      }

      return {
        success: true,
        mapping: channelMapping,
        isUpdate: !!existingMapping
      };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get all channel mappings
   * @returns {Promise<Array>} Channel mappings
   */
  async getChannelMappings() {
    const docs = await this.channelMappingCollection.find({ isActive: true }).toArray();
    return docs.map(doc => ChannelMapping.fromDocument(doc));
  }

  /**
   * Get integration status
   * @returns {Promise<Object>} Integration status
   */
  async getIntegrationStatus() {
    const integration = await this.getActiveIntegration();
    
    if (!integration) {
      return {
        connected: false,
        error: 'No active integration found'
      };
    }

    return {
      connected: true,
      teamName: integration.teamName,
      teamId: integration.teamId,
      connectedAt: integration.connectedAt,
      lastTestAt: integration.lastTestAt
    };
  }
}

module.exports = SlackIntegrationService;
