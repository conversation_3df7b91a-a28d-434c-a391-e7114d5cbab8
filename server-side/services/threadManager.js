const { ObjectId } = require('mongodb');
const ThreadTracker = require('../models/threadTracker');
const SlackService = require('./slackService');

/**
 * Thread Manager Service
 * Handles Slack thread creation and management with concurrency safety
 */
class ThreadManager {
  constructor(db) {
    this.db = db;
    this.threadTrackerCollection = db.collection('slack_thread_trackers');
  }

  /**
   * Find or create a monthly thread for a user's report type
   * @param {Object} params - Thread parameters
   * @param {ObjectId} params.userId - User ID
   * @param {string} params.reportType - Report type
   * @param {string} params.slackChannelId - Slack channel ID
   * @param {Date} params.submissionDate - Report submission date
   * @param {SlackService} params.slackService - Slack service instance
   * @param {Object} params.user - User data for thread creation
   * @param {Object} params.report - Report data for thread creation
   * @returns {Promise<Object>} Thread information
   */
  async findOrCreateThread(params) {
    const { userId, reportType, slackChannelId, submissionDate, slackService, user, report } = params;
    
    // Generate month key
    const monthKey = ThreadTracker.generateMonthKey(submissionDate);
    
    // Try to find existing thread
    let threadTracker = await this.findExistingThread(userId, reportType, slackChannelId, monthKey);
    
    if (threadTracker) {
      return {
        success: true,
        threadTs: threadTracker.threadTs,
        threadUrl: threadTracker.threadUrl,
        isNewThread: false,
        threadTracker
      };
    }

    // Create new thread with concurrency safety
    return this.createNewThread({
      userId,
      reportType,
      slackChannelId,
      monthKey,
      slackService,
      user,
      report
    });
  }

  /**
   * Find existing thread
   * @param {ObjectId} userId - User ID
   * @param {string} reportType - Report type
   * @param {string} slackChannelId - Slack channel ID
   * @param {string} monthKey - Month key (YYYY-MM)
   * @returns {Promise<ThreadTracker|null>} Thread tracker or null
   */
  async findExistingThread(userId, reportType, slackChannelId, monthKey) {
    const query = {
      userId: new ObjectId(userId),
      reportType,
      slackChannelId,
      monthKey,
      isActive: true
    };

    const doc = await this.threadTrackerCollection.findOne(query);
    return doc ? ThreadTracker.fromDocument(doc) : null;
  }

  /**
   * Create new thread with concurrency safety
   * @param {Object} params - Thread creation parameters
   * @returns {Promise<Object>} Thread creation result
   */
  async createNewThread(params) {
    const { userId, reportType, slackChannelId, monthKey, slackService, user, report } = params;
    
    const maxRetries = 3;
    let attempt = 0;

    while (attempt < maxRetries) {
      attempt++;

      try {
        // Create root message in Slack
        const rootMessage = slackService.createReportMessage(report, user, true);
        const slackResult = await slackService.postMessage(slackChannelId, rootMessage);

        if (!slackResult.success) {
          throw new Error(`Failed to post root message: ${slackResult.error}`);
        }

        // Create ThreadTracker document
        const threadTracker = new ThreadTracker({
          userId: new ObjectId(userId),
          reportType,
          slackChannelId,
          monthKey,
          threadTs: slackResult.timestamp,
          messageCount: 1,
          lastMessageAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        });

        // Generate thread URL (we'll need team ID from integration)
        const integration = await this.getSlackIntegration();
        if (integration && integration.teamId) {
          threadTracker.generateThreadUrl(integration.teamId);
        }

        // Insert with unique constraint handling
        try {
          const insertResult = await this.threadTrackerCollection.insertOne(threadTracker.toObject());
          
          return {
            success: true,
            threadTs: threadTracker.threadTs,
            threadUrl: threadTracker.threadUrl,
            isNewThread: true,
            threadTracker: ThreadTracker.fromDocument({ ...threadTracker.toObject(), _id: insertResult.insertedId })
          };
        } catch (insertError) {
          if (insertError.code === 11000) {
            // Duplicate key error - another thread was created concurrently
            console.log(`Concurrent thread creation detected for user ${userId}, reportType ${reportType}, month ${monthKey}. Retrying...`);
            
            // Find the existing thread that was created by another process
            const existingThread = await this.findExistingThread(userId, reportType, slackChannelId, monthKey);
            if (existingThread) {
              return {
                success: true,
                threadTs: existingThread.threadTs,
                threadUrl: existingThread.threadUrl,
                isNewThread: false,
                threadTracker: existingThread
              };
            }
            
            // If we can't find the existing thread, retry
            continue;
          } else {
            throw insertError;
          }
        }
      } catch (error) {
        console.error(`Thread creation attempt ${attempt} failed:`, error);
        
        if (attempt >= maxRetries) {
          return {
            success: false,
            error: `Failed to create thread after ${maxRetries} attempts: ${error.message}`
          };
        }
        
        // Wait before retrying
        await this.sleep(1000 * attempt);
      }
    }

    return {
      success: false,
      error: 'Failed to create thread after maximum retries'
    };
  }

  /**
   * Post message to existing thread
   * @param {string} threadTs - Thread timestamp
   * @param {string} slackChannelId - Slack channel ID
   * @param {SlackService} slackService - Slack service instance
   * @param {Object} user - User data
   * @param {Object} report - Report data
   * @returns {Promise<Object>} Post result
   */
  async postToThread(threadTs, slackChannelId, slackService, user, report) {
    try {
      // Create reply message
      const replyMessage = slackService.createReportMessage(report, user, false);
      
      // Post to thread
      const slackResult = await slackService.postThreadMessage(slackChannelId, threadTs, replyMessage);
      
      if (!slackResult.success) {
        return {
          success: false,
          error: `Failed to post to thread: ${slackResult.error}`
        };
      }

      // Update thread tracker
      await this.updateThreadTracker(threadTs, slackChannelId);

      return {
        success: true,
        messageTs: slackResult.timestamp
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update thread tracker with new message
   * @param {string} threadTs - Thread timestamp
   * @param {string} slackChannelId - Slack channel ID
   */
  async updateThreadTracker(threadTs, slackChannelId) {
    await this.threadTrackerCollection.updateOne(
      { threadTs, slackChannelId },
      {
        $inc: { messageCount: 1 },
        $set: { 
          lastMessageAt: new Date(),
          updatedAt: new Date()
        }
      }
    );
  }

  /**
   * Get active Slack integration
   * @returns {Promise<Object|null>} Slack integration
   */
  async getSlackIntegration() {
    const integrationCollection = this.db.collection('slack_integrations');
    return integrationCollection.findOne({ isActive: true });
  }

  /**
   * Get thread statistics for a user
   * @param {ObjectId} userId - User ID
   * @param {string} monthKey - Month key (optional)
   * @returns {Promise<Array>} Thread statistics
   */
  async getUserThreadStats(userId, monthKey = null) {
    const query = {
      userId: new ObjectId(userId),
      isActive: true
    };

    if (monthKey) {
      query.monthKey = monthKey;
    }

    return this.threadTrackerCollection.find(query).toArray();
  }

  /**
   * Get all active threads for a report type
   * @param {string} reportType - Report type
   * @param {string} monthKey - Month key (optional)
   * @returns {Promise<Array>} Active threads
   */
  async getActiveThreads(reportType, monthKey = null) {
    const query = {
      reportType,
      isActive: true
    };

    if (monthKey) {
      query.monthKey = monthKey;
    }

    return this.threadTrackerCollection.find(query).toArray();
  }

  /**
   * Deactivate old threads (cleanup utility)
   * @param {number} monthsOld - Number of months old to deactivate
   * @returns {Promise<number>} Number of deactivated threads
   */
  async deactivateOldThreads(monthsOld = 6) {
    const cutoffDate = new Date();
    cutoffDate.setMonth(cutoffDate.getMonth() - monthsOld);
    const cutoffMonthKey = ThreadTracker.generateMonthKey(cutoffDate);

    const result = await this.threadTrackerCollection.updateMany(
      { 
        monthKey: { $lt: cutoffMonthKey },
        isActive: true
      },
      {
        $set: { 
          isActive: false,
          updatedAt: new Date()
        }
      }
    );

    return result.modifiedCount;
  }

  /**
   * Sleep utility for retry delays
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = ThreadManager;
