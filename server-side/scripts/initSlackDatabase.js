const { MongoClient, ServerApiVersion } = require('mongodb');
const DatabaseInit = require('../utils/databaseInit');
require('dotenv').config();

/**
 * Database initialization script for Slack integration
 * Run this script to set up collections and indexes
 */

const uri = "mongodb://127.0.0.1:27017/?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh+2.3.8";

const client = new MongoClient(uri, {
  serverApi: {
    version: ServerApiVersion.v1,
    strict: true,
    deprecationErrors: true,
  },
});

async function initializeSlackDatabase() {
  try {
    console.log('🚀 Starting Slack integration database initialization...');
    
    // Connect to MongoDB
    await client.connect();
    console.log('✅ Connected to MongoDB');
    
    const db = client.db("wpdevDB");
    const dbInit = new DatabaseInit(db);
    
    // Initialize Slack collections and indexes
    await dbInit.initializeSlackCollections();
    
    // Get and display collection statistics
    console.log('\n📊 Collection Statistics:');
    const stats = await dbInit.getCollectionStats();
    
    for (const [collectionName, collectionStats] of Object.entries(stats)) {
      if (collectionStats.error) {
        console.log(`❌ ${collectionName}: Error - ${collectionStats.error}`);
      } else {
        console.log(`✅ ${collectionName}:`);
        console.log(`   Documents: ${collectionStats.documentCount}`);
        console.log(`   Indexes: ${collectionStats.indexCount}`);
        console.log(`   Index Keys: ${JSON.stringify(collectionStats.indexes)}`);
      }
    }
    
    console.log('\n🎉 Slack integration database initialization completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Set up your Slack app and get a bot token');
    console.log('2. Configure environment variables in .env file');
    console.log('3. Use the admin panel to connect your Slack integration');
    console.log('4. Set up channel mappings for your report types');
    
  } catch (error) {
    console.error('❌ Error initializing Slack database:', error);
    process.exit(1);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

async function resetSlackDatabase() {
  try {
    console.log('🧹 Starting Slack integration database reset...');
    
    // Connect to MongoDB
    await client.connect();
    console.log('✅ Connected to MongoDB');
    
    const db = client.db("wpdevDB");
    const dbInit = new DatabaseInit(db);
    
    // Drop Slack collections
    await dbInit.dropSlackCollections();
    
    // Recreate collections and indexes
    await dbInit.initializeSlackCollections();
    
    console.log('🎉 Slack integration database reset completed successfully!');
    
  } catch (error) {
    console.error('❌ Error resetting Slack database:', error);
    process.exit(1);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

async function showSlackDatabaseStats() {
  try {
    console.log('📊 Getting Slack integration database statistics...');
    
    // Connect to MongoDB
    await client.connect();
    console.log('✅ Connected to MongoDB');
    
    const db = client.db("wpdevDB");
    const dbInit = new DatabaseInit(db);
    
    // Get and display collection statistics
    const stats = await dbInit.getCollectionStats();
    
    console.log('\n📊 Collection Statistics:');
    for (const [collectionName, collectionStats] of Object.entries(stats)) {
      if (collectionStats.error) {
        console.log(`❌ ${collectionName}: Error - ${collectionStats.error}`);
      } else {
        console.log(`\n✅ ${collectionName}:`);
        console.log(`   Documents: ${collectionStats.documentCount}`);
        console.log(`   Indexes: ${collectionStats.indexCount}`);
        
        if (collectionStats.indexes.length > 0) {
          console.log('   Index Details:');
          collectionStats.indexes.forEach((index, i) => {
            console.log(`     ${i + 1}. ${JSON.stringify(index)}`);
          });
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error getting Slack database stats:', error);
    process.exit(1);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Command line interface
const command = process.argv[2];

switch (command) {
  case 'init':
    initializeSlackDatabase();
    break;
  case 'reset':
    resetSlackDatabase();
    break;
  case 'stats':
    showSlackDatabaseStats();
    break;
  default:
    console.log('Slack Integration Database Management');
    console.log('');
    console.log('Usage: node initSlackDatabase.js <command>');
    console.log('');
    console.log('Commands:');
    console.log('  init   - Initialize Slack integration collections and indexes');
    console.log('  reset  - Drop and recreate all Slack integration collections');
    console.log('  stats  - Show current database statistics');
    console.log('');
    console.log('Examples:');
    console.log('  node scripts/initSlackDatabase.js init');
    console.log('  node scripts/initSlackDatabase.js stats');
    console.log('  node scripts/initSlackDatabase.js reset');
    break;
}
