const jwt = require("jsonwebtoken");
const { ObjectId } = require("mongodb");

/**
 * Verify JWT token middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const verifyToken = (req, res, next) => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({
      success: false,
      error: "Access token is required",
    });
  }

  const token = authHeader.split(" ")[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      success: false,
      error: "Access token is required",
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    console.error("JWT verification error:", error);

    if (error.name === "TokenExpiredError") {
      return res.status(401).json({
        success: false,
        error: "Token has expired",
      });
    }

    if (error.name === "JsonWebTokenError") {
      return res.status(401).json({
        success: false,
        error: "Invalid token",
      });
    }

    return res.status(401).json({
      success: false,
      error: "Token verification failed",
    });
  }
};

/**
 * Verify admin role middleware
 * Requires verifyToken to be called first
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const verifyAdmin = (db) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: "Authentication required",
        });
      }

      // Get user from database to check role
      const userCollection = db.collection("users");
      const email = req.user.email;
      const query = { email: email };
      const user = await userCollection.findOne(query);

      if (!user) {
        return res.status(401).json({
          success: false,
          error: "User not found",
        });
      }

      const isAdmin = user.role === "admin" || user.role === "superadmin";
      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          error: "Admin access required",
        });
      }

      // Add user info to request for use in handlers
      req.user.userId = user._id;
      req.user.role = user.role;

      next();
    } catch (error) {
      console.error("Admin verification error:", error);
      return res.status(500).json({
        success: false,
        error: "Authorization verification failed",
      });
    }
  };
};

/**
 * Verify super admin role middleware
 * Requires verifyToken to be called first
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const verifySuperAdmin = (db) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: "Authentication required",
        });
      }

      // Get user from database to check role
      const userCollection = db.collection("users");
      const email = req.user.email;
      const query = { email: email };
      const user = await userCollection.findOne(query);

      if (!user) {
        return res.status(401).json({
          success: false,
          error: "User not found",
        });
      }

      // Check if user has superadmin role
      if (user.role !== "superadmin") {
        return res.status(403).json({
          success: false,
          error: "Super admin access required",
        });
      }

      // Add user info to request for use in handlers
      req.user.userId = user._id;
      req.user.role = user.role;

      next();
    } catch (error) {
      console.error("Super admin verification error:", error);
      return res.status(500).json({
        success: false,
        error: "Authorization verification failed",
      });
    }
  };
};

/**
 * Verify user owns resource or is admin
 * @param {string} userIdField - Field name containing user ID in request body/params
 * @returns {Function} Middleware function
 */
const verifyOwnershipOrAdmin = (userIdField = "userId") => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: "Authentication required",
        });
      }

      // Admin users can access any resource
      if (req.user.role === "admin" || req.user.role === "superadmin") {
        return next();
      }

      // Get user ID from request
      const resourceUserId = req.body[userIdField] || req.params[userIdField];

      if (!resourceUserId) {
        return res.status(400).json({
          success: false,
          error: `${userIdField} is required`,
        });
      }

      // Check if user owns the resource
      const currentUserId = req.user.userId || req.user._id;

      if (resourceUserId.toString() !== currentUserId.toString()) {
        return res.status(403).json({
          success: false,
          error: "Access denied: You can only access your own resources",
        });
      }

      next();
    } catch (error) {
      console.error("Ownership verification error:", error);
      return res.status(500).json({
        success: false,
        error: "Authorization verification failed",
      });
    }
  };
};

/**
 * Optional authentication middleware
 * Sets req.user if token is valid, but doesn't require authentication
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const optionalAuth = (req, res, next) => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return next();
  }

  const token = authHeader.split(" ")[1];

  if (!token) {
    return next();
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
  } catch (error) {
    // Ignore token errors for optional auth
    console.log("Optional auth token error (ignored):", error.message);
  }

  next();
};

/**
 * Rate limiting middleware for sensitive operations
 * @param {Object} options - Rate limiting options
 * @returns {Function} Middleware function
 */
const rateLimitSensitive = (options = {}) => {
  const windowMs = options.windowMs || 15 * 60 * 1000; // 15 minutes
  const maxAttempts = options.maxAttempts || 5;
  const requests = new Map();

  return (req, res, next) => {
    const key = req.ip + (req.user ? req.user.userId : "");
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean old entries
    for (const [reqKey, timestamps] of requests.entries()) {
      const validTimestamps = timestamps.filter((ts) => ts > windowStart);
      if (validTimestamps.length === 0) {
        requests.delete(reqKey);
      } else {
        requests.set(reqKey, validTimestamps);
      }
    }

    // Check current requests
    const userRequests = requests.get(key) || [];
    const recentRequests = userRequests.filter((ts) => ts > windowStart);

    if (recentRequests.length >= maxAttempts) {
      return res.status(429).json({
        success: false,
        error: "Too many requests. Please try again later.",
        retryAfter: Math.ceil(
          (Math.min(...recentRequests) + windowMs - now) / 1000
        ),
      });
    }

    // Record this request
    recentRequests.push(now);
    requests.set(key, recentRequests);

    next();
  };
};

module.exports = {
  verifyToken,
  verifyAdmin,
  verifySuperAdmin,
  verifyOwnershipOrAdmin,
  optionalAuth,
  rateLimitSensitive,
};
