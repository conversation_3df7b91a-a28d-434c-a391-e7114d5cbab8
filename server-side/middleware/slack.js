const SlackIntegrationService = require('../services/slackIntegrationService');

/**
 * Middleware to check if Slack integration is active
 * @param {Object} db - MongoDB database instance
 * @returns {Function} Middleware function
 */
const requireSlackIntegration = (db) => {
  const slackService = new SlackIntegrationService(db);
  
  return async (req, res, next) => {
    try {
      const status = await slackService.getIntegrationStatus();
      
      if (!status.connected) {
        return res.status(503).json({
          success: false,
          error: 'Slack integration is not configured or connected',
          code: 'SLACK_NOT_CONNECTED'
        });
      }
      
      // Add integration status to request for use in handlers
      req.slackIntegration = status;
      next();
      
    } catch (error) {
      console.error('Error checking Slack integration status:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to check Slack integration status'
      });
    }
  };
};

/**
 * Middleware to validate report type for Slack posting
 * @param {Object} db - MongoDB database instance
 * @returns {Function} Middleware function
 */
const validateReportType = (db) => {
  const slackService = new SlackIntegrationService(db);
  
  return async (req, res, next) => {
    try {
      const { reportType } = req.body;
      
      if (!reportType) {
        return res.status(400).json({
          success: false,
          error: 'Report type is required'
        });
      }
      
      // Check if there's a channel mapping for this report type
      const channelMapping = await slackService.getChannelMapping(reportType);
      
      if (!channelMapping) {
        // Don't fail the request, just mark that Slack should be skipped
        req.skipSlack = true;
        req.skipSlackReason = `No channel mapping found for report type: ${reportType}`;
      } else {
        req.channelMapping = channelMapping;
      }
      
      next();
      
    } catch (error) {
      console.error('Error validating report type:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to validate report type'
      });
    }
  };
};

/**
 * Middleware to handle Slack rate limiting
 * @param {Object} options - Rate limiting options
 * @returns {Function} Middleware function
 */
const slackRateLimit = (options = {}) => {
  const windowMs = options.windowMs || 60000; // 1 minute
  const maxRequests = options.maxRequests || 50; // Slack allows 50 requests per minute
  const requests = new Map();
  
  return (req, res, next) => {
    const key = 'slack_api_calls';
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Clean old entries
    const userRequests = requests.get(key) || [];
    const validRequests = userRequests.filter(timestamp => timestamp > windowStart);
    
    if (validRequests.length >= maxRequests) {
      const oldestRequest = Math.min(...validRequests);
      const resetTime = oldestRequest + windowMs;
      const retryAfter = Math.ceil((resetTime - now) / 1000);
      
      return res.status(429).json({
        success: false,
        error: 'Slack API rate limit exceeded',
        retryAfter,
        code: 'SLACK_RATE_LIMITED'
      });
    }
    
    // Record this request
    validRequests.push(now);
    requests.set(key, validRequests);
    
    next();
  };
};

/**
 * Middleware to validate Slack channel ID format
 * @param {string} fieldName - Name of the field containing channel ID
 * @returns {Function} Middleware function
 */
const validateSlackChannelId = (fieldName = 'slackChannelId') => {
  return (req, res, next) => {
    const channelId = req.body[fieldName] || req.params[fieldName] || req.query[fieldName];
    
    if (!channelId) {
      return res.status(400).json({
        success: false,
        error: `${fieldName} is required`
      });
    }
    
    // Slack channel IDs should start with C (public channel) or G (private channel/group)
    if (!/^[CG][A-Z0-9]{8,}$/.test(channelId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid Slack channel ID format'
      });
    }
    
    next();
  };
};

/**
 * Middleware to validate Slack bot token format
 * @param {string} fieldName - Name of the field containing bot token
 * @returns {Function} Middleware function
 */
const validateSlackBotToken = (fieldName = 'botToken') => {
  return (req, res, next) => {
    const botToken = req.body[fieldName];
    
    if (!botToken) {
      return res.status(400).json({
        success: false,
        error: `${fieldName} is required`
      });
    }
    
    if (typeof botToken !== 'string') {
      return res.status(400).json({
        success: false,
        error: `${fieldName} must be a string`
      });
    }
    
    // Slack bot tokens should start with xoxb-
    if (!botToken.startsWith('xoxb-')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid bot token format. Bot tokens should start with "xoxb-"'
      });
    }
    
    // Basic length validation (Slack bot tokens are typically 55+ characters)
    if (botToken.length < 50) {
      return res.status(400).json({
        success: false,
        error: 'Bot token appears to be too short'
      });
    }
    
    next();
  };
};

/**
 * Middleware to log Slack operations for monitoring
 * @param {string} operation - Operation name
 * @returns {Function} Middleware function
 */
const logSlackOperation = (operation) => {
  return (req, res, next) => {
    const startTime = Date.now();
    
    // Log the start of the operation
    console.log(`[Slack] Starting ${operation} - User: ${req.user?.email || 'unknown'} - Time: ${new Date().toISOString()}`);
    
    // Override res.json to log the result
    const originalJson = res.json;
    res.json = function(data) {
      const duration = Date.now() - startTime;
      const success = data.success !== false;
      
      console.log(`[Slack] Completed ${operation} - Success: ${success} - Duration: ${duration}ms - User: ${req.user?.email || 'unknown'}`);
      
      if (!success && data.error) {
        console.log(`[Slack] Error in ${operation}: ${data.error}`);
      }
      
      return originalJson.call(this, data);
    };
    
    next();
  };
};

/**
 * Middleware to handle Slack integration errors gracefully
 * @param {Function} handler - Route handler function
 * @returns {Function} Wrapped handler function
 */
const handleSlackErrors = (handler) => {
  return async (req, res, next) => {
    try {
      await handler(req, res, next);
    } catch (error) {
      console.error('Slack integration error:', error);
      
      // Check if it's a known Slack error
      if (error.code === 'rate_limited') {
        return res.status(429).json({
          success: false,
          error: 'Slack API rate limit exceeded',
          retryAfter: error.retryAfter || 60,
          code: 'SLACK_RATE_LIMITED'
        });
      }
      
      if (error.code === 'channel_not_found') {
        return res.status(400).json({
          success: false,
          error: 'Slack channel not found or bot not invited to channel',
          code: 'SLACK_CHANNEL_NOT_FOUND'
        });
      }
      
      if (error.code === 'invalid_auth') {
        return res.status(401).json({
          success: false,
          error: 'Invalid Slack bot token',
          code: 'SLACK_INVALID_AUTH'
        });
      }
      
      // Generic Slack error
      return res.status(500).json({
        success: false,
        error: 'Slack integration error occurred',
        code: 'SLACK_ERROR'
      });
    }
  };
};

module.exports = {
  requireSlackIntegration,
  validateReportType,
  slackRateLimit,
  validateSlackChannelId,
  validateSlackBotToken,
  logSlackOperation,
  handleSlackErrors
};
