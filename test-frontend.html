<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WP Dev Daily Desk - Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold text-center mb-8">WP Dev Daily Desk - Test Page</h1>
        
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Server Status</h2>
            <div id="server-status" class="text-gray-600">Checking server...</div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">JWT Token Test</h2>
            <div class="mb-4">
                <input type="email" id="email" placeholder="Enter email" class="w-full p-2 border rounded">
            </div>
            <button onclick="getJWT()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Get JWT Token</button>
            <div id="jwt-result" class="mt-4 text-sm"></div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">API Test Results</h2>
            <div id="api-results" class="text-sm text-gray-600">No tests run yet</div>
        </div>
    </div>

    <script>
        // Test server connection
        async function testServer() {
            try {
                const response = await fetch('http://localhost:500/');
                const text = await response.text();
                document.getElementById('server-status').innerHTML = 
                    `<span class="text-green-600">✓ Server is running: ${text}</span>`;
            } catch (error) {
                document.getElementById('server-status').innerHTML = 
                    `<span class="text-red-600">✗ Server error: ${error.message}</span>`;
            }
        }

        // Get JWT token
        async function getJWT() {
            const email = document.getElementById('email').value;
            if (!email) {
                alert('Please enter an email');
                return;
            }

            try {
                const response = await fetch('http://localhost:500/jwt', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: email })
                });
                
                const result = await response.json();
                document.getElementById('jwt-result').innerHTML = 
                    `<div class="bg-gray-100 p-2 rounded"><strong>JWT Token:</strong><br>${JSON.stringify(result, null, 2)}</div>`;
                
                // Store token for further tests
                if (result.token) {
                    localStorage.setItem('jwt-token', result.token);
                    testProtectedEndpoints(result.token);
                }
            } catch (error) {
                document.getElementById('jwt-result').innerHTML = 
                    `<span class="text-red-600">Error: ${error.message}</span>`;
            }
        }

        // Test protected endpoints
        async function testProtectedEndpoints(token) {
            const results = [];
            
            // Test users endpoint
            try {
                const response = await fetch('http://localhost:500/users', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                const data = await response.json();
                results.push(`<div class="mb-2"><strong>Users endpoint:</strong> ${response.status} - ${JSON.stringify(data).substring(0, 100)}...</div>`);
            } catch (error) {
                results.push(`<div class="mb-2 text-red-600"><strong>Users endpoint error:</strong> ${error.message}</div>`);
            }

            document.getElementById('api-results').innerHTML = results.join('');
        }

        // Run initial tests
        testServer();
    </script>
</body>
</html>
